#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快手采集工具 - EXE打包脚本
支持Python 3.13，使用PyInstaller打包成单个可执行文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
import json

class ExeBuilder:
    """EXE构建器"""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.dist_dir = self.project_root / "dist"
        self.build_dir = self.project_root / "build"
        self.spec_file = self.project_root / "kuaishou_collector.spec"
        
        # 项目信息
        self.app_name = "快手采集工具"
        self.app_version = "1.0.0"
        self.main_script = "main.py"
        
        # 需要包含的Python文件
        self.python_files = [
            "main.py",
            "data_collector.py",
            "product_query.py",
            "category_parser.py"
            # cookie_exporter.py 已整合到 main.py 中，无需单独包含
        ]
        
        # 需要包含的数据文件
        self.data_files = [
            "类目响应数据.md",
            "类目补充响应数据.md",
            "响应数据格式.md",
            "商品成交量响应数据格式.md",
            "商品成交量数载荷源码格式.md",
            "商品成交量查询功能说明.md",
            "总成交指数响应数据格式.md",
            "总成交指数载荷源码格式.md",
            "分销销量 达人数响应数据格式.md",
            "分销销量 达人数载荷源码格式.md",
            "请求载荷源码格式.md",
            "需求.md",
            "README.md"
        ]
        
        # 需要包含的目录
        self.data_dirs = [
            "data",
            "logs"
        ]
        
    def check_environment(self):
        """检查环境"""
        print("=" * 60)
        print("🔍 检查构建环境...")
        print("=" * 60)
        
        # 检查Python版本
        python_version = sys.version_info
        print(f"✓ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        if python_version < (3, 7):
            print("❌ Python版本过低，需要3.7+")
            return False
            
        # 检查必要的Python文件
        missing_files = []
        for file in self.python_files:
            if not (self.project_root / file).exists():
                missing_files.append(file)
                
        if missing_files:
            print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
            return False
        else:
            print(f"✓ 所有Python文件存在: {len(self.python_files)}个")
            
        # 检查数据文件
        existing_data_files = []
        for file in self.data_files:
            if (self.project_root / file).exists():
                existing_data_files.append(file)
                
        print(f"✓ 找到数据文件: {len(existing_data_files)}/{len(self.data_files)}个")
        
        return True
        
    def install_dependencies(self):
        """安装依赖"""
        print("\n" + "=" * 60)
        print("📦 安装构建依赖...")
        print("=" * 60)
        
        # 必要的依赖包
        dependencies = [
            "pyinstaller>=5.0",
            "PyQt5>=5.15.0",
            "PyQtWebEngine>=5.15.0", 
            "requests>=2.25.0",
            "pandas>=1.3.0",
            "openpyxl>=3.0.0"
        ]
        
        print("正在安装依赖包...")
        for dep in dependencies:
            try:
                print(f"  安装 {dep}...")
                result = subprocess.run([
                    sys.executable, "-m", "pip", "install", dep
                ], capture_output=True, text=True, check=True)
                print(f"  ✓ {dep} 安装成功")
            except subprocess.CalledProcessError as e:
                print(f"  ❌ {dep} 安装失败: {e}")
                print(f"     错误输出: {e.stderr}")
                return False
                
        print("✓ 所有依赖安装完成")
        return True

    def detect_qt_version(self):
        """检测可用的Qt版本"""
        try:
            import PyQt5
            print("✓ 检测到PyQt5")
            return 5
        except ImportError:
            try:
                import PyQt6
                print("✓ 检测到PyQt6")
                return 6
            except ImportError:
                print("❌ 未检测到PyQt5或PyQt6")
                return None

    def create_spec_file(self):
        """创建PyInstaller spec文件"""
        print("\n" + "=" * 60)
        print("📝 创建PyInstaller配置文件...")
        print("=" * 60)
        
        # 构建数据文件列表
        datas = []
        
        # 添加数据文件
        for file in self.data_files:
            file_path = self.project_root / file
            if file_path.exists():
                datas.append(f"('{file}', '.')")
                
        # 添加数据目录
        for dir_name in self.data_dirs:
            dir_path = self.project_root / dir_name
            if dir_path.exists():
                datas.append(f"('{dir_name}', '{dir_name}')")
                
        datas_str = ",\n    ".join(datas)
        
        # 检测可用的Qt版本并构建隐藏导入列表
        qt_version = self.detect_qt_version()

        if qt_version == 5:
            hidden_imports = [
                "'PyQt5.QtCore'",
                "'PyQt5.QtGui'",
                "'PyQt5.QtWidgets'",
                "'PyQt5.QtWebEngineWidgets'",
                "'PyQt5.QtWebEngineCore'",
            ]
        else:  # PyQt6
            hidden_imports = [
                "'PyQt6.QtCore'",
                "'PyQt6.QtGui'",
                "'PyQt6.QtWidgets'",
                "'PyQt6.QtWebEngineWidgets'",
                "'PyQt6.QtWebEngineCore'",
            ]

        # 添加通用导入
        hidden_imports.extend([
            "'requests'",
            "'pandas'",
            "'openpyxl'",
            "'json'",
            "'pathlib'",
            "'datetime'",
            "'concurrent.futures'",
            "'threading'",
            "'logging'"
        ])
        
        hidden_imports_str = ",\n    ".join(hidden_imports)

        # 构建排除列表，排除不需要的Qt版本
        excludes = []
        if qt_version == 5:
            excludes = ["'PyQt6'", "'PyQt6.QtCore'", "'PyQt6.QtGui'", "'PyQt6.QtWidgets'"]
        else:
            excludes = ["'PyQt5'", "'PyQt5.QtCore'", "'PyQt5.QtGui'", "'PyQt5.QtWidgets'"]

        excludes_str = ",\n    ".join(excludes)

        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['{self.main_script}'],
    pathex=[],
    binaries=[],
    datas=[
    {datas_str}
    ],
    hiddenimports=[
    {hidden_imports_str}
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[
    {excludes_str}
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='{self.app_name}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    version='version_info.txt',
    icon=None,
)
'''
        
        # 写入spec文件
        with open(self.spec_file, 'w', encoding='utf-8') as f:
            f.write(spec_content)
            
        print(f"✓ 配置文件已创建: {self.spec_file}")
        return True
        
    def create_version_info(self):
        """创建版本信息文件"""
        version_info = f'''# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1,0,0,0),
    prodvers=(1,0,0,0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'080404B0',
        [StringStruct(u'CompanyName', u'快手采集工具'),
        StringStruct(u'FileDescription', u'{self.app_name}'),
        StringStruct(u'FileVersion', u'{self.app_version}'),
        StringStruct(u'InternalName', u'{self.app_name}'),
        StringStruct(u'LegalCopyright', u'Copyright © 2024'),
        StringStruct(u'OriginalFilename', u'{self.app_name}.exe'),
        StringStruct(u'ProductName', u'{self.app_name}'),
        StringStruct(u'ProductVersion', u'{self.app_version}')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [2052, 1200])])
  ]
)
'''
        
        version_file = self.project_root / "version_info.txt"
        with open(version_file, 'w', encoding='utf-8') as f:
            f.write(version_info)
            
        print(f"✓ 版本信息文件已创建: {version_file}")
        return True
        
    def build_exe(self):
        """构建EXE文件"""
        print("\n" + "=" * 60)
        print("🔨 开始构建EXE文件...")
        print("=" * 60)
        
        try:
            # 清理之前的构建
            if self.build_dir.exists():
                shutil.rmtree(self.build_dir)
                print("✓ 清理构建目录")
                
            if self.dist_dir.exists():
                shutil.rmtree(self.dist_dir)
                print("✓ 清理输出目录")
                
            # 运行PyInstaller
            cmd = [
                sys.executable, "-m", "PyInstaller",
                "--clean",
                "--noconfirm", 
                str(self.spec_file)
            ]
            
            print("正在执行PyInstaller...")
            print(f"命令: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, cwd=self.project_root, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✓ PyInstaller执行成功")
                
                # 检查生成的EXE文件
                exe_file = self.dist_dir / f"{self.app_name}.exe"
                if exe_file.exists():
                    file_size = exe_file.stat().st_size / (1024 * 1024)  # MB
                    print(f"✓ EXE文件已生成: {exe_file}")
                    print(f"✓ 文件大小: {file_size:.1f} MB")
                    return True
                else:
                    print("❌ EXE文件未找到")
                    return False
            else:
                print("❌ PyInstaller执行失败")
                print("错误输出:")
                print(result.stderr)
                return False
                
        except Exception as e:
            print(f"❌ 构建过程出错: {e}")
            return False
            
    def cleanup(self):
        """清理临时文件"""
        print("\n" + "=" * 60)
        print("🧹 清理临时文件...")
        print("=" * 60)
        
        # 清理构建目录
        if self.build_dir.exists():
            shutil.rmtree(self.build_dir)
            print("✓ 清理构建目录")
            
        # 清理spec文件
        if self.spec_file.exists():
            self.spec_file.unlink()
            print("✓ 清理spec文件")
            
        # 清理版本信息文件
        version_file = self.project_root / "version_info.txt"
        if version_file.exists():
            version_file.unlink()
            print("✓ 清理版本信息文件")
            
    def build(self):
        """执行完整的构建流程"""
        print("🚀 快手采集工具 - EXE打包工具")
        print(f"📁 项目目录: {self.project_root}")
        
        # 1. 检查环境
        if not self.check_environment():
            print("❌ 环境检查失败，构建终止")
            return False
            
        # 2. 安装依赖
        if not self.install_dependencies():
            print("❌ 依赖安装失败，构建终止")
            return False
            
        # 3. 创建配置文件
        if not self.create_spec_file():
            print("❌ 配置文件创建失败，构建终止")
            return False
            
        # 4. 创建版本信息
        if not self.create_version_info():
            print("❌ 版本信息创建失败，构建终止")
            return False
            
        # 5. 构建EXE
        if not self.build_exe():
            print("❌ EXE构建失败")
            return False
            
        # 6. 清理临时文件
        self.cleanup()
        
        print("\n" + "=" * 60)
        print("🎉 构建完成！")
        print("=" * 60)
        print(f"✓ EXE文件位置: {self.dist_dir / f'{self.app_name}.exe'}")
        print("✓ 可以直接运行，无需安装Python环境")
        print("✓ 包含所有必要的依赖和数据文件")
        
        return True

def main():
    """主函数"""
    builder = ExeBuilder()
    success = builder.build()
    
    if success:
        print("\n🎊 打包成功！您可以在dist目录中找到可执行文件。")
    else:
        print("\n💥 打包失败！请检查错误信息并重试。")
        
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
