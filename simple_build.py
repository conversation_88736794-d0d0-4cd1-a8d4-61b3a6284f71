#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快手采集工具 - 简化版EXE打包脚本
适用于Python 3.13，一键打包成EXE
"""

import os
import sys
import subprocess
from pathlib import Path

def install_pyinstaller():
    """安装PyInstaller"""
    print("📦 正在安装PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✓ PyInstaller安装成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ PyInstaller安装失败")
        return False

def check_dependencies():
    """检查并安装必要依赖"""
    print("🔍 检查依赖包...")

    required_packages = [
        "PyQt5",
        "PyQtWebEngine",
        "requests",
        "pandas",
        "openpyxl",
        "pyinstaller"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.lower().replace('pyqt', 'PyQt'))
            print(f"✓ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} 未安装")
    
    if missing_packages:
        print(f"\n正在安装缺失的包: {', '.join(missing_packages)}")
        for package in missing_packages:
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                print(f"✓ {package} 安装成功")
            except subprocess.CalledProcessError:
                print(f"❌ {package} 安装失败")
                return False
    
    return True

def detect_qt_version():
    """检测可用的Qt版本"""
    try:
        import PyQt5  # noqa: F401
        print("✓ 检测到PyQt5，将使用PyQt5进行打包")
        return 5
    except ImportError:
        try:
            import PyQt6  # noqa: F401
            print("✓ 检测到PyQt6，将使用PyQt6进行打包")
            return 6
        except ImportError:
            print("❌ 未检测到PyQt5或PyQt6")
            return None

def build_exe():
    """构建EXE文件"""
    print("\n🔨 开始构建EXE文件...")

    # 检查主文件是否存在
    if not Path("main.py").exists():
        print("❌ 未找到main.py文件")
        return False

    # 检测Qt版本
    qt_version = detect_qt_version()
    if qt_version is None:
        print("❌ 未找到可用的Qt版本")
        return False
    
    # 根据Qt版本构建PyInstaller命令
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",  # 打包成单个文件
        "--windowed",  # 无控制台窗口
        "--name=快手采集工具",  # 指定输出文件名
        "--add-data=类目响应数据.md;.",  # 添加数据文件
        "--add-data=data;data",  # 添加data目录
    ]

    # 根据Qt版本添加隐藏导入和排除项
    if qt_version == 5:
        cmd.extend([
            "--hidden-import=PyQt5.QtCore",
            "--hidden-import=PyQt5.QtGui",
            "--hidden-import=PyQt5.QtWidgets",
            "--hidden-import=PyQt5.QtWebEngineWidgets",
            "--hidden-import=PyQt5.QtWebEngineCore",
            "--exclude-module=PyQt6",
            "--exclude-module=PyQt6.QtCore",
            "--exclude-module=PyQt6.QtGui",
            "--exclude-module=PyQt6.QtWidgets",
        ])
    else:  # PyQt6
        cmd.extend([
            "--hidden-import=PyQt6.QtCore",
            "--hidden-import=PyQt6.QtGui",
            "--hidden-import=PyQt6.QtWidgets",
            "--hidden-import=PyQt6.QtWebEngineWidgets",
            "--hidden-import=PyQt6.QtWebEngineCore",
            "--exclude-module=PyQt5",
            "--exclude-module=PyQt5.QtCore",
            "--exclude-module=PyQt5.QtGui",
            "--exclude-module=PyQt5.QtWidgets",
        ])

    # 添加通用隐藏导入
    cmd.extend([
        "--hidden-import=requests",
        "--hidden-import=pandas",
        "--hidden-import=json",
        "--hidden-import=pathlib",
        "--hidden-import=concurrent.futures",
        "main.py"
    ])
    
    # 添加其他可选的数据文件
    optional_files = [
        "类目补充响应数据.md",
        "响应数据格式.md", 
        "商品成交量响应数据格式.md",
        "README.md"
    ]
    
    for file in optional_files:
        if Path(file).exists():
            cmd.insert(-1, f"--add-data={file};.")
    
    print("执行命令:")
    print(" ".join(cmd))
    print("\n这可能需要几分钟时间，请耐心等待...")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ 构建成功！")
            
            # 检查生成的文件
            exe_file = Path("dist/快手采集工具.exe")
            if exe_file.exists():
                file_size = exe_file.stat().st_size / (1024 * 1024)
                print(f"✓ EXE文件: {exe_file}")
                print(f"✓ 文件大小: {file_size:.1f} MB")
                return True
            else:
                print("❌ EXE文件未生成")
                return False
        else:
            print("❌ 构建失败")
            print("错误信息:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        return False

def main():
    """主函数"""
    print("🚀 快手采集工具 - 简化版EXE打包工具")
    print("=" * 50)
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ Python版本过低，需要3.7+")
        input("按回车键退出...")
        return
    
    print(f"✓ Python版本: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    
    # 1. 安装PyInstaller
    if not install_pyinstaller():
        input("按回车键退出...")
        return
    
    # 2. 检查依赖
    if not check_dependencies():
        print("❌ 依赖检查失败")
        input("按回车键退出...")
        return
    
    # 3. 构建EXE
    if build_exe():
        print("\n🎉 打包完成！")
        print("📁 EXE文件位置: dist/快手采集工具.exe")
        print("✅ 可以直接运行，无需Python环境")
    else:
        print("\n💥 打包失败！")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
