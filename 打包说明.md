# 快手采集工具 - EXE打包说明

## 概述

本文档说明如何将快手采集工具打包成独立的EXE可执行文件，适用于Python 3.13环境。

## 项目结构分析

经过分析，您的项目包含以下核心文件：

### Python源码文件
- `main.py` - 主程序，包含PyQt5/6界面和主要逻辑
- `data_collector.py` - 数据采集模块，处理网络请求和数据处理
- `product_query.py` - 商品查询模块，支持多线程查询
- `category_parser.py` - 类目解析模块，处理类目数据
- `cookie_exporter.py` - Cookie管理模块，集成浏览器功能

### 数据文件
- `类目响应数据.md` - 类目数据源文件
- `类目补充响应数据.md` - 补充类目数据
- 各种响应数据格式文档
- `data/` 目录 - 存储Cookie和类目数据
- `logs/` 目录 - 日志文件

### 依赖分析
- **PyQt5/PyQt6** - GUI框架
- **PyQtWebEngine** - 浏览器组件（用于Cookie获取）
- **requests** - HTTP请求库
- **pandas** - 数据处理
- **openpyxl** - Excel文件处理
- **concurrent.futures** - 多线程支持
- **pathlib, json, datetime** - 标准库

## 打包方案

我为您提供了四种打包方案：

### 方案一：Qt冲突修复打包（强烈推荐）⭐
如果遇到Qt版本冲突错误，使用此方案：
直接双击运行 `修复Qt冲突并打包.bat` 文件，会自动：
1. 检测Qt版本冲突
2. 自动卸载冲突的Qt版本
3. 安装正确的PyQt5版本
4. 执行打包过程

### 方案二：一键打包
直接双击运行 `打包EXE.bat` 文件，会自动：
1. 检查Python环境
2. 安装必要依赖
3. 执行打包过程

### 方案三：简化脚本
运行 `simple_build.py`：
```bash
python simple_build.py
```

### 方案四：完整脚本
运行 `build_exe.py`（功能更全面）：
```bash
python build_exe.py
```

## 使用步骤

### 1. 准备环境
确保您的系统已安装：
- Python 3.13（或3.7+）
- pip包管理器

### 2. 执行打包
选择以下任一方式：

**遇到Qt冲突时（推荐）：**
双击 `修复Qt冲突并打包.bat` 文件

**正常情况：**
双击 `打包EXE.bat` 文件

**命令行方式：**
```bash
# 方式1：修复Qt冲突
python fix_qt_conflict.py

# 方式2：使用简化脚本
python simple_build.py

# 方式3：使用完整脚本
python build_exe.py
```

### 3. 等待完成
打包过程可能需要5-10分钟，请耐心等待。

### 4. 获取结果
打包完成后，在 `dist/` 目录下会生成：
- `快手采集工具.exe` - 可执行文件

## 打包特性

### 包含的功能
✅ 完整的GUI界面  
✅ 数据采集功能  
✅ 商品查询功能  
✅ Cookie管理功能  
✅ 类目解析功能  
✅ 数据导出功能  
✅ 所有必要的数据文件  

### 打包优化
- **单文件打包** - 生成单个EXE文件，便于分发
- **无控制台** - 运行时不显示命令行窗口
- **依赖内置** - 包含所有Python依赖，无需安装Python环境
- **数据文件** - 自动包含所有必要的配置和数据文件

## 常见问题

### Q: 出现Qt版本冲突错误怎么办？⭐
A: 这是最常见的问题，错误信息类似：
```
ERROR: Aborting build process due to attempt to collect multiple Qt bindings packages
```
**解决方案：**
1. 双击运行 `修复Qt冲突并打包.bat`
2. 或运行 `python fix_qt_conflict.py`
3. 工具会自动卸载冲突的Qt版本并重新安装正确版本

### Q: 打包失败怎么办？
A: 检查以下几点：
1. Python版本是否为3.7+
2. 网络连接是否正常（需要下载依赖）
3. 磁盘空间是否充足（至少1GB）
4. 是否有杀毒软件干扰
5. 是否存在Qt版本冲突（使用修复工具）

### Q: 生成的EXE文件很大？
A: 这是正常的，因为包含了：
- Python解释器
- PyQt5/6框架
- 浏览器引擎组件
- 所有依赖库
通常大小在100-300MB之间

### Q: 运行EXE时报错？
A: 可能的原因：
1. Windows版本过低（需要Windows 10+）
2. 缺少Visual C++运行库
3. 被杀毒软件误报

### Q: 如何减小文件大小？
A: 可以尝试：
1. 使用 `--exclude-module` 排除不需要的模块
2. 使用UPX压缩（在脚本中已启用）
3. 分离数据文件（不推荐，会影响便携性）

## 技术细节

### PyInstaller配置
- 使用 `--onefile` 生成单文件
- 使用 `--windowed` 隐藏控制台
- 自动检测并包含隐藏导入
- 包含所有数据文件和目录

### 兼容性处理
- 同时支持PyQt5和PyQt6
- 自动检测可用的Qt版本
- 处理不同Python版本的差异

### 错误处理
- 完整的错误检查和提示
- 详细的构建日志
- 自动清理临时文件

## 分发说明

生成的EXE文件可以：
- 直接在Windows系统上运行
- 无需安装Python环境
- 无需额外配置
- 支持Windows 10/11

## 更新维护

如需更新程序：
1. 修改源代码
2. 重新运行打包脚本
3. 替换旧的EXE文件

## 许可证

请确保遵守所有依赖库的许可证要求，特别是：
- PyQt5/6的商业使用许可
- 其他第三方库的使用条款

---

**注意：** 首次打包可能需要下载较多依赖包，请确保网络连接稳定。
