@echo off
chcp 65001 >nul
title 快手采集工具 - 依赖安装

echo.
echo ========================================
echo    快手采集工具 - 依赖安装
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Python，请先安装Python 3.7+
    echo.
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✓ 检测到Python环境
python --version

echo.
echo 📦 开始安装依赖包...
echo.

:: 升级pip
echo 正在升级pip...
python -m pip install --upgrade pip

echo.
echo 正在安装依赖包...

:: 安装核心依赖
python -m pip install PyQt5>=5.15.0
python -m pip install PyQtWebEngine>=5.15.0
python -m pip install requests>=2.25.0
python -m pip install pandas>=1.3.0
python -m pip install openpyxl>=3.0.0
python -m pip install pyinstaller>=5.0

echo.
echo ✅ 依赖安装完成！
echo.
echo 现在您可以：
echo 1. 运行 python main.py 启动程序
echo 2. 运行 打包EXE.bat 打包成EXE文件
echo.

pause
