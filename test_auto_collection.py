#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动采集功能测试脚本
"""

import json
from pathlib import Path

def test_category_data_loading():
    """测试类目数据加载"""
    data_dir = Path("data")
    category_file = data_dir / "Category data.txt"
    
    if not category_file.exists():
        print("类目数据文件不存在，请先解析类目数据")
        return None
        
    try:
        with open(category_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        # 如果数据包含过滤设置，分离类目数据和过滤设置
        if "filter_settings" in data:
            category_data = {k: v for k, v in data.items() if k != "filter_settings"}
        else:
            category_data = data
            
        print(f"成功加载类目数据，共 {len(category_data)} 个一级类目")
        return category_data
        
    except Exception as e:
        print(f"加载类目数据失败: {e}")
        return None

def test_auto_collection_categories_generation(category_data, start_category, start_level):
    """测试自动采集类目列表生成"""
    categories = []
    
    if start_level == "一级类目":
        categories = get_all_subcategories_from_first_level(category_data, start_category)
    elif start_level == "二级类目":
        categories = get_all_subcategories_from_second_level(category_data, start_category)
    elif start_level == "三级类目":
        categories = get_all_subcategories_from_third_level(category_data, start_category)
    elif start_level == "四级类目":
        # 只采集该四级类目
        categories = [build_category_path(category_data, start_category, "四级类目")]
        
    return categories

def get_all_subcategories_from_first_level(category_data, first_level_name):
    """从一级类目获取所有子类目"""
    categories = []

    if first_level_name not in category_data:
        return categories

    first_level_data = category_data[first_level_name]

    # 遍历二级类目
    for second_name, second_data in first_level_data.get('children', {}).items():
        # 遍历三级类目
        for third_name, third_data in second_data.get('children', {}).items():
            # 遍历四级类目（四级类目是数组格式）
            fourth_level_children = third_data.get('children', [])
            if isinstance(fourth_level_children, list):
                for fourth_data in fourth_level_children:
                    category_path = {
                        "一级类目": first_level_name,
                        "二级类目": second_name,
                        "三级类目": third_name,
                        "四级类目": fourth_data.get('name', ''),
                        "四级类目_key": fourth_data.get('key', '')
                    }
                    categories.append(category_path)

    return categories

def get_all_subcategories_from_second_level(category_data, second_level_name):
    """从二级类目获取所有子类目"""
    categories = []

    # 找到包含该二级类目的一级类目
    first_level_name = None
    for first_name, first_data in category_data.items():
        if second_level_name in first_data.get('children', {}):
            first_level_name = first_name
            break

    if not first_level_name:
        return categories

    second_data = category_data[first_level_name]['children'][second_level_name]

    # 遍历三级类目
    for third_name, third_data in second_data.get('children', {}).items():
        # 遍历四级类目（四级类目是数组格式）
        fourth_level_children = third_data.get('children', [])
        if isinstance(fourth_level_children, list):
            for fourth_data in fourth_level_children:
                category_path = {
                    "一级类目": first_level_name,
                    "二级类目": second_level_name,
                    "三级类目": third_name,
                    "四级类目": fourth_data.get('name', ''),
                    "四级类目_key": fourth_data.get('key', '')
                }
                categories.append(category_path)

    return categories

def get_all_subcategories_from_third_level(category_data, third_level_name):
    """从三级类目获取所有子类目"""
    categories = []

    # 找到包含该三级类目的一级和二级类目
    first_level_name = None
    second_level_name = None

    for first_name, first_data in category_data.items():
        for second_name, second_data in first_data.get('children', {}).items():
            if third_level_name in second_data.get('children', {}):
                first_level_name = first_name
                second_level_name = second_name
                break
        if first_level_name:
            break

    if not first_level_name or not second_level_name:
        return categories

    third_data = category_data[first_level_name]['children'][second_level_name]['children'][third_level_name]

    # 遍历四级类目（四级类目是数组格式）
    fourth_level_children = third_data.get('children', [])
    if isinstance(fourth_level_children, list):
        for fourth_data in fourth_level_children:
            category_path = {
                "一级类目": first_level_name,
                "二级类目": second_level_name,
                "三级类目": third_level_name,
                "四级类目": fourth_data.get('name', ''),
                "四级类目_key": fourth_data.get('key', '')
            }
            categories.append(category_path)

    return categories

def build_category_path(category_data, category_name, level):
    """构建类目路径"""
    # 简化实现，返回空字典
    return {}

def main():
    """主测试函数"""
    print("=== 自动采集功能测试 ===")
    
    # 测试类目数据加载
    category_data = test_category_data_loading()
    if not category_data:
        return
        
    # 显示一级类目列表
    print("\n可用的一级类目:")
    for i, first_level in enumerate(category_data.keys(), 1):
        print(f"{i}. {first_level}")
        
    # 测试从第一个一级类目生成自动采集列表
    first_category = list(category_data.keys())[0]
    print(f"\n测试从一级类目 '{first_category}' 生成自动采集列表...")
    
    auto_categories = test_auto_collection_categories_generation(category_data, first_category, "一级类目")
    
    print(f"生成了 {len(auto_categories)} 个待采集类目")
    
    # 显示前5个类目作为示例
    print("\n前5个类目示例:")
    for i, category in enumerate(auto_categories[:5], 1):
        path = f"{category['一级类目']} > {category['二级类目']} > {category['三级类目']} > {category['四级类目']}"
        print(f"{i}. {path}")
        
    if len(auto_categories) > 5:
        print(f"... 还有 {len(auto_categories) - 5} 个类目")

if __name__ == "__main__":
    main()
