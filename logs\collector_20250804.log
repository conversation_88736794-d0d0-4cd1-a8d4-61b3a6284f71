2025-08-04 02:33:11,942 - INFO - DataCollector资源清理完成
2025-08-04 02:42:49,786 - INFO - 设置筛选条件: {'日期': '本周 (2025-08-04-2025-08-10)', '一级类目': '个护日百行业', '二级类目': '', '三级类目': '', '四级类目': '', '售卖渠道': '全部', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-08-04 02:42:49,787 - INFO - 设置过滤设置: {'成交指数最小值': 1500, '成交指数最大值': 999999, '渠道占比最小值': 85, '渠道占比最大值': 105}
2025-08-04 02:42:49,811 - INFO - 类目数据加载成功
2025-08-04 02:42:49,812 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-08-04", "currentEndDay": "2025-08-10", "compareStartDay": "2025-07-28", "compareEndDay": "2025-08-03", "param": [{"code": "industryId", "value": ["个护日百行业"]}, {"code": "saleChannel", "value": ["all"]}, {"code": "saleType", "value": ["all"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-08-04 02:42:49,816 - INFO - 成功加载 8 个Cookie
2025-08-04 02:42:50,492 - WARNING - 响应中没有数据
2025-08-04 02:42:58,494 - INFO - 设置筛选条件: {'日期': '本周 (2025-08-04-2025-08-10)', '一级类目': '个护日百行业', '二级类目': '', '三级类目': '', '四级类目': '', '售卖渠道': '全部', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-08-04 02:42:58,494 - INFO - 设置过滤设置: {'成交指数最小值': 1500, '成交指数最大值': 999999, '渠道占比最小值': 85, '渠道占比最大值': 105}
2025-08-04 02:42:58,517 - INFO - 类目数据加载成功
2025-08-04 02:42:58,518 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-08-04", "currentEndDay": "2025-08-10", "compareStartDay": "2025-07-28", "compareEndDay": "2025-08-03", "param": [{"code": "industryId", "value": ["个护日百行业"]}, {"code": "saleChannel", "value": ["all"]}, {"code": "saleType", "value": ["all"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-08-04 02:42:58,520 - INFO - 成功加载 8 个Cookie
2025-08-04 02:42:59,216 - WARNING - 响应中没有数据
2025-08-04 02:43:03,020 - INFO - 设置筛选条件: {'日期': '2025-07-28-2025-08-03', '一级类目': '个护日百行业', '二级类目': '', '三级类目': '', '四级类目': '', '售卖渠道': '全部', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-08-04 02:43:03,021 - INFO - 设置过滤设置: {'成交指数最小值': 1500, '成交指数最大值': 999999, '渠道占比最小值': 85, '渠道占比最大值': 105}
2025-08-04 02:43:03,045 - INFO - 类目数据加载成功
2025-08-04 02:43:03,045 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-07-28", "currentEndDay": "2025-08-03", "compareStartDay": "2025-07-21", "compareEndDay": "2025-07-27", "param": [{"code": "industryId", "value": ["个护日百行业"]}, {"code": "saleChannel", "value": ["all"]}, {"code": "saleType", "value": ["all"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-08-04 02:43:03,047 - INFO - 成功加载 8 个Cookie
2025-08-04 02:43:04,431 - INFO - 成功获取 100 条基础数据
2025-08-04 02:43:04,432 - INFO - 成交指数过滤：原始 100 条，过滤后 100 条
2025-08-04 02:43:04,437 - INFO - 成功加载 8 个Cookie
2025-08-04 02:43:04,437 - INFO - 成功加载 8 个Cookie
2025-08-04 02:43:04,440 - INFO - 成功加载 8 个Cookie
2025-08-04 02:43:05,406 - INFO - 成功加载 8 个Cookie
2025-08-04 02:43:05,451 - INFO - 成功加载 8 个Cookie
2025-08-04 02:43:05,572 - INFO - 成功加载 8 个Cookie
2025-08-04 02:43:11,597 - INFO - DataCollector资源清理完成
2025-08-04 02:56:01,373 - INFO - DataCollector资源清理完成
2025-08-04 02:56:31,804 - INFO - 设置筛选条件: {'日期': '本周 (2025-08-04-2025-08-10)', '一级类目': '个护日百行业', '二级类目': '', '三级类目': '', '四级类目': '', '售卖渠道': '全部', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-08-04 02:56:31,805 - INFO - 设置过滤设置: {'成交指数最小值': 1500, '成交指数最大值': 999999, '渠道占比最小值': 85, '渠道占比最大值': 105}
2025-08-04 02:56:31,829 - INFO - 类目数据加载成功
2025-08-04 02:56:31,830 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-08-04", "currentEndDay": "2025-08-10", "compareStartDay": "2025-07-28", "compareEndDay": "2025-08-03", "param": [{"code": "industryId", "value": ["个护日百行业"]}, {"code": "saleChannel", "value": ["all"]}, {"code": "saleType", "value": ["all"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-08-04 02:56:31,833 - INFO - 成功加载 9 个Cookie
2025-08-04 02:56:32,512 - WARNING - 响应中没有数据
2025-08-04 02:56:37,564 - INFO - 设置筛选条件: {'日期': '2025-07-28-2025-08-03', '一级类目': '个护日百行业', '二级类目': '', '三级类目': '', '四级类目': '', '售卖渠道': '全部', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-08-04 02:56:37,565 - INFO - 设置过滤设置: {'成交指数最小值': 1500, '成交指数最大值': 999999, '渠道占比最小值': 85, '渠道占比最大值': 105}
2025-08-04 02:56:37,588 - INFO - 类目数据加载成功
2025-08-04 02:56:37,589 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-07-28", "currentEndDay": "2025-08-03", "compareStartDay": "2025-07-21", "compareEndDay": "2025-07-27", "param": [{"code": "industryId", "value": ["个护日百行业"]}, {"code": "saleChannel", "value": ["all"]}, {"code": "saleType", "value": ["all"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-08-04 02:56:37,590 - INFO - 成功加载 9 个Cookie
2025-08-04 02:56:38,231 - INFO - 成功获取 100 条基础数据
2025-08-04 02:56:38,231 - INFO - 成交指数过滤：原始 100 条，过滤后 100 条
2025-08-04 02:56:38,235 - INFO - 成功加载 9 个Cookie
2025-08-04 02:56:38,236 - INFO - 成功加载 9 个Cookie
2025-08-04 02:56:38,236 - INFO - 成功加载 9 个Cookie
2025-08-04 02:56:39,075 - INFO - 成功加载 9 个Cookie
2025-08-04 02:56:39,092 - INFO - 成功加载 9 个Cookie
2025-08-04 02:56:39,098 - INFO - 成功加载 9 个Cookie
2025-08-04 02:56:39,950 - INFO - 成功加载 9 个Cookie
2025-08-04 02:56:39,966 - INFO - 成功加载 9 个Cookie
2025-08-04 02:56:39,981 - INFO - 成功加载 9 个Cookie
2025-08-04 02:56:40,953 - INFO - 成功加载 9 个Cookie
2025-08-04 02:56:40,980 - INFO - 成功加载 9 个Cookie
2025-08-04 02:56:40,980 - INFO - 成功加载 9 个Cookie
2025-08-04 02:56:42,885 - INFO - DataCollector资源清理完成
2025-08-04 09:14:53,117 - INFO - DataCollector资源清理完成
2025-08-04 09:15:22,572 - INFO - DataCollector资源清理完成
2025-08-04 09:15:30,029 - INFO - DataCollector资源清理完成
2025-08-04 20:25:13,294 - INFO - DataCollector资源清理完成
2025-08-04 23:00:43,414 - INFO - DataCollector资源清理完成
2025-08-04 23:05:56,349 - INFO - 设置筛选条件: {'日期': '上周 (2025-07-28-2025-08-03)', '一级类目': '个护日百行业', '二级类目': '个护清洁', '三级类目': '身体护理', '四级类目': '身体乳/油/喷雾', '售卖渠道': '全部', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-08-04 23:05:56,350 - INFO - 设置过滤设置: {'成交指数最小值': 1500, '成交指数最大值': 999999, '渠道占比最小值': 85, '渠道占比最大值': 105}
2025-08-04 23:05:56,378 - INFO - 类目数据加载成功
2025-08-04 23:05:56,379 - ERROR - 解析日期范围失败: time data '上周 (2025-07-28' does not match format '%Y-%m-%d'
2025-08-04 23:06:04,100 - INFO - 设置筛选条件: {'日期': '上周 (2025-07-28-2025-08-03)', '一级类目': '个护日百行业', '二级类目': '个护清洁', '三级类目': '身体护理', '四级类目': '身体乳/油/喷雾', '售卖渠道': '全部', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-08-04 23:06:04,101 - INFO - 设置过滤设置: {'成交指数最小值': 1500, '成交指数最大值': 999999, '渠道占比最小值': 85, '渠道占比最大值': 105}
2025-08-04 23:06:04,132 - INFO - 类目数据加载成功
2025-08-04 23:06:04,134 - ERROR - 解析日期范围失败: time data '上周 (2025-07-28' does not match format '%Y-%m-%d'
2025-08-04 23:06:16,252 - INFO - 设置筛选条件: {'日期': '2025-07-21-2025-07-27', '一级类目': '个护日百行业', '二级类目': '个护清洁', '三级类目': '身体护理', '四级类目': '身体乳/油/喷雾', '售卖渠道': '全部', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-08-04 23:06:16,252 - INFO - 设置过滤设置: {'成交指数最小值': 1500, '成交指数最大值': 999999, '渠道占比最小值': 85, '渠道占比最大值': 105}
2025-08-04 23:06:16,274 - INFO - 类目数据加载成功
2025-08-04 23:06:16,275 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-07-21", "currentEndDay": "2025-07-27", "compareStartDay": "2025-07-14", "compareEndDay": "2025-07-20", "param": [{"code": "industryId", "value": ["个护日百行业"]}, {"code": "categoryLevel1Id", "value": ["1060"]}, {"code": "categoryLevel2Id", "value": ["1662"]}, {"code": "categoryLevel3Id", "value": ["1669"]}, {"code": "saleChannel", "value": ["all"]}, {"code": "saleType", "value": ["all"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-08-04 23:06:16,277 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:17,141 - INFO - 成功获取 100 条基础数据
2025-08-04 23:06:17,141 - INFO - 遇到成交指数 1484 小于最小值 1500，停止采集
2025-08-04 23:06:17,142 - INFO - 成交指数过滤：原始 100 条，过滤后 42 条
2025-08-04 23:06:17,145 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:17,146 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:17,149 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:18,161 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:18,169 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:18,307 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:19,357 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:19,372 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:19,519 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:20,454 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:20,636 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:20,726 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:21,873 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:21,936 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:22,002 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:22,993 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:23,047 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:23,190 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:24,131 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:24,169 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:24,369 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:25,239 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:25,253 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:25,434 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:26,232 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:26,289 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:26,380 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:27,229 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:27,278 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:27,350 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:28,260 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:28,278 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:28,445 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:29,273 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:29,304 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:29,476 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:30,258 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:30,278 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:30,415 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:31,251 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:31,287 - INFO - 成功加载 9 个Cookie
2025-08-04 23:06:31,448 - INFO - 成功加载 9 个Cookie
2025-08-04 23:07:11,108 - INFO - 设置筛选条件: {'日期': '上周 (2025-07-28-2025-08-03)', '一级类目': '个护日百行业', '二级类目': '个护清洁', '三级类目': '身体护理', '四级类目': '身体乳/油/喷雾', '售卖渠道': '全部', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-08-04 23:07:11,109 - INFO - 设置过滤设置: {'成交指数最小值': 1500, '成交指数最大值': 999999, '渠道占比最小值': 85, '渠道占比最大值': 105}
2025-08-04 23:07:11,131 - INFO - 类目数据加载成功
2025-08-04 23:07:11,132 - ERROR - 解析日期范围失败: time data '上周 (2025-07-28' does not match format '%Y-%m-%d'
2025-08-04 23:07:47,564 - INFO - 设置筛选条件: {'日期': '上周 (2025-07-28-2025-08-03)', '一级类目': '个护日百行业', '二级类目': '个护清洁', '三级类目': '身体护理', '四级类目': '身体乳/油/喷雾', '售卖渠道': '全部', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-08-04 23:07:47,564 - INFO - 设置过滤设置: {'成交指数最小值': 1500, '成交指数最大值': 999999, '渠道占比最小值': 85, '渠道占比最大值': 105}
2025-08-04 23:07:47,587 - INFO - 类目数据加载成功
2025-08-04 23:07:47,589 - ERROR - 解析日期范围失败: time data '上周 (2025-07-28' does not match format '%Y-%m-%d'
2025-08-04 23:12:18,788 - INFO - DataCollector资源清理完成
2025-08-04 23:13:38,859 - INFO - 设置筛选条件: {'日期': '上周 (2025-07-28-2025-08-03)', '一级类目': '', '二级类目': '', '三级类目': '', '四级类目': '', '售卖渠道': '全部', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-08-04 23:13:38,860 - INFO - 设置过滤设置: {'成交指数最小值': 1500, '成交指数最大值': 999999, '渠道占比最小值': 85, '渠道占比最大值': 105}
2025-08-04 23:13:38,882 - INFO - 类目数据加载成功
2025-08-04 23:13:38,883 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-07-28", "currentEndDay": "2025-08-03", "compareStartDay": "2025-07-21", "compareEndDay": "2025-07-27", "param": [{"code": "saleChannel", "value": ["all"]}, {"code": "saleType", "value": ["all"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-08-04 23:13:38,886 - INFO - 成功加载 9 个Cookie
2025-08-04 23:13:40,133 - INFO - 成功获取 100 条基础数据
2025-08-04 23:13:40,133 - INFO - 成交指数过滤：原始 100 条，过滤后 100 条
2025-08-04 23:13:40,138 - INFO - 成功加载 9 个Cookie
2025-08-04 23:13:40,142 - INFO - 成功加载 9 个Cookie
2025-08-04 23:13:40,144 - INFO - 成功加载 9 个Cookie
2025-08-04 23:13:41,152 - INFO - 成功加载 9 个Cookie
2025-08-04 23:13:41,182 - INFO - 成功加载 9 个Cookie
2025-08-04 23:13:41,190 - INFO - 成功加载 9 个Cookie
2025-08-04 23:13:42,209 - INFO - 成功加载 9 个Cookie
2025-08-04 23:13:42,210 - INFO - 成功加载 9 个Cookie
2025-08-04 23:13:42,245 - INFO - 成功加载 9 个Cookie
2025-08-04 23:13:43,208 - INFO - 成功加载 9 个Cookie
2025-08-04 23:13:43,215 - INFO - 成功加载 9 个Cookie
2025-08-04 23:13:43,301 - INFO - 成功加载 9 个Cookie
2025-08-04 23:13:44,217 - INFO - 成功加载 9 个Cookie
2025-08-04 23:13:44,274 - INFO - 成功加载 9 个Cookie
2025-08-04 23:13:44,351 - INFO - 成功加载 9 个Cookie
2025-08-04 23:13:45,246 - INFO - 成功加载 9 个Cookie
2025-08-04 23:13:45,298 - INFO - 成功加载 9 个Cookie
2025-08-04 23:13:45,316 - INFO - 成功加载 9 个Cookie
2025-08-04 23:16:43,024 - INFO - 设置筛选条件: {'日期': '上周 (2025-07-28-2025-08-03)', '一级类目': '个护日百行业', '二级类目': '', '三级类目': '', '四级类目': '', '售卖渠道': '商品卡', '售卖形式': '自卖', '品牌商品': '全部', '大牌大补': '全部'}
2025-08-04 23:16:43,025 - INFO - 设置过滤设置: {'成交指数最小值': 1500, '成交指数最大值': 999999, '渠道占比最小值': 0.0, '渠道占比最大值': 200.0}
2025-08-04 23:16:43,047 - INFO - 类目数据加载成功
2025-08-04 23:16:43,047 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-07-28", "currentEndDay": "2025-08-03", "compareStartDay": "2025-07-21", "compareEndDay": "2025-07-27", "param": [{"code": "industryId", "value": ["个护日百行业"]}, {"code": "saleChannel", "value": ["商品卡"]}, {"code": "saleType", "value": ["否"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-08-04 23:16:43,052 - INFO - 成功加载 9 个Cookie
2025-08-04 23:16:44,235 - INFO - 成功获取 100 条基础数据
2025-08-04 23:16:44,236 - INFO - 成交指数过滤：原始 100 条，过滤后 100 条
2025-08-04 23:16:44,240 - INFO - 成功加载 9 个Cookie
2025-08-04 23:16:44,240 - INFO - 成功加载 9 个Cookie
2025-08-04 23:16:44,240 - INFO - 成功加载 9 个Cookie
2025-08-04 23:16:45,286 - INFO - 成功加载 9 个Cookie
2025-08-04 23:16:45,286 - INFO - 成功加载 9 个Cookie
2025-08-04 23:16:45,294 - INFO - 成功加载 9 个Cookie
2025-08-04 23:16:53,776 - INFO - 设置筛选条件: {'日期': '上周 (2025-07-28-2025-08-03)', '一级类目': '个护日百行业', '二级类目': '', '三级类目': '', '四级类目': '', '售卖渠道': '商品卡', '售卖形式': '自卖', '品牌商品': '全部', '大牌大补': '全部'}
2025-08-04 23:16:53,815 - INFO - 设置过滤设置: {'成交指数最小值': 1500, '成交指数最大值': 999999, '渠道占比最小值': 85.0, '渠道占比最大值': 105.0}
2025-08-04 23:16:53,835 - INFO - 类目数据加载成功
2025-08-04 23:16:53,835 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-07-28", "currentEndDay": "2025-08-03", "compareStartDay": "2025-07-21", "compareEndDay": "2025-07-27", "param": [{"code": "industryId", "value": ["个护日百行业"]}, {"code": "saleChannel", "value": ["商品卡"]}, {"code": "saleType", "value": ["否"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-08-04 23:16:53,837 - INFO - 成功加载 9 个Cookie
2025-08-04 23:16:54,512 - INFO - 成功获取 100 条基础数据
2025-08-04 23:16:54,514 - INFO - 成交指数过滤：原始 100 条，过滤后 100 条
2025-08-04 23:16:54,517 - INFO - 成功加载 9 个Cookie
2025-08-04 23:16:54,518 - INFO - 成功加载 9 个Cookie
2025-08-04 23:16:54,519 - INFO - 成功加载 9 个Cookie
2025-08-04 23:16:55,325 - INFO - 成功加载 9 个Cookie
2025-08-04 23:16:55,360 - INFO - 成功加载 9 个Cookie
2025-08-04 23:16:55,375 - INFO - 成功加载 9 个Cookie
2025-08-04 23:16:56,181 - INFO - 成功加载 9 个Cookie
2025-08-04 23:16:56,191 - INFO - 成功加载 9 个Cookie
2025-08-04 23:16:56,287 - INFO - 成功加载 9 个Cookie
2025-08-04 23:17:23,145 - INFO - DataCollector资源清理完成
