你是一名极其优秀具有20年经验的产品经理和精通py语言的工程师
# 一、UI界面要求
#### （一）UI界面整体要求
1.窗口是长方形的。
2.界面风格简单
3.状态显示区域增加边框和背景色
请设计一个简洁明了的用户界面，具体要求如下：  
1.采用简约的设计风格，避免过多的装饰元素  
2.使用清晰的布局结构，功能区域划分明确  
3.选择简单易读的字体和适中的字号  
4.采用简洁的配色方案，主要使用2-3种颜色  
5.按钮和控件设计要简单直观，易于理解和操作  
6.减少不必要的视觉干扰，突出核心功能  
7.确保界面响应式设计，在不同屏幕尺寸下都能良好显示  
8.保持一致的视觉风格和交互模式
#### （二）上半部分15%的区域显示以下设置
1.条件筛选设置
- 采用典型的水平筛选栏布局
- 顶部水平排列9个等高的下拉筛选器，每个筛选器形成单行表头+单行内容的简化表格布局
- 采用白底灰框的极简设计，所有元素严格对齐，形成清晰的视觉线
- 严格的网格系统：所有控件等高度、等间距
- 从左到右的筛选逻辑递进（日期→一级类目→二级类目→三级类目→四级类目→售卖渠道→售卖形式→品牌商品→大牌大补）
- （日期）筛选器默认本周的日期，垂直下拉式日期区间选择器，日期区间格式，采用 ​“YYYY-MM-DD” 标准格式，每行显示开始日期 （周一）+结束日期（周日），用”-“分隔，提供连续52周的筛选选项。
- ​（一级类目→二级类目→三级类目→四级类目）筛选器未选择时显示空白
        - 类目数据需要在启动软件时加载”Category data.txt“类目数据文件然后进行适配下拉选项，若没有”Category data.txt“文件时点击类目筛选器提醒用户进行解析类目。
- （售卖渠道/售卖形式/品牌商品/大牌大补）筛选器采用相同的"全部"默认值，
        - 售卖渠道筛选器的下拉选项显示“全部、直播间、短视频、商品卡“
        - 品牌商品/大牌大补筛选器的下拉选项显示“全部、自卖、分销”
        - 售卖形式筛选器下拉选项显示“全部、商品卡、直播、短视频"

2.在条件筛选设置下显示以下按钮
- 登录
- 开始
- 停止
- 导出
- 解析类目
#### （三）下半部分85%为数据展示区域
1.视觉层级：
- 顶部采用深蓝色字体表头，与白色背景形成高对比度，强化字段分类
- 网格线使用浅灰色细线，在保证数据分区清晰的同时避免视觉干扰
2.信息架构：
- 9个字段排列，表头依次为：   
    ​排名→标题→链接→​成交指数→​总成交指数→渠道占比→​​支付件数→成交商家数→类目
3.交互设计：
- 居中对齐的版式符合数据表格的阅读惯性

# 二、功能要求
#### （一）登录功能
- 点击“登录”按钮后，调用cookie_exporter.py
- 软件打开时检测是否有Cookies，没有Cookies的情况下，禁止点击“解析类目、登录、开始、停止、导出”按钮
#### （二）解析类目功能
> 仔细阅读并且理解”类目响应数据.md“文件，根据文件的格式，写一个解析类目的功能。
- 点击”解析类目“按钮后，自动解析文件名为”类目响应数据.md“的类目数据文件， 整理成树形结构行业分类数据，解析完成的数据命名为”Category data.txt“保存到根目录的data文件里
- 树形结构格式：
个护日百行业（一级类目 | categoryPid:）
└── ​个护清洁（二级类目 | categoryPid: "个护日百行业"）  
  └── ​身体护理​（三级类目 | categoryPid: "1060"）  
    ├── 抑菌/消毒乳膏（key: "29366"）  
    └── 足部护理（key: "21117"）
- 然后根据”Category data.txt“适配筛选类目条件的设置项，适配筛选器的下拉选项。
#### （三）采集功能
1.数据展示适配
- 严格遵循"响应数据格式.md"进行数据映射
- 根据“响应数据格式.md”适配数据展示区域的展示功能，字段映射规范：

| 响应字段          | 展示名称  | 数据类型 | 展示要求                 |
| ------------- | ----- | ---- | -------------------- |
| rankIndex     | 排名    | 整数   | 居中显示                 |
| itemTitle     | 标题    | 字符串  | 最大显示50字符             |
| itemLinkUrl   | 链接    | URL  | 可点击的超链接              |
| payItemNum    | 销售件数  | 整数   | 千位分隔符                |
| payAmtPerItem | 客单价   | 浮点数  | 保留2位小数               |
| sourceValue   | 成交指数  | 浮点数  | 整数                   |
| (待开发)         | 总成交指数 | -    | 预留位置暂时留空             |
| (待开发)         | 渠道占比  | -    | 预留位置暂时留空             |
| (待开发)         | 成交商家数 | -    | 预留位置暂时留空             |
| (待开发)         | 达人数   | -    | 预留位置暂时留空             |
| -             | 类目    | -    | 根据用户设置的类目筛选设置，显示对应类目 |

2.采集流程
阶段一：请求准备
- 输入：用户筛选条件
- 处理：
    - 验证条件有效性
    - 生成符合“请求载荷源码格式.md"格式规范的JSON载荷
    - 检查cookies.txt可用性
- 输出：有效的请求载荷

阶段二：请求执行发送请求；
请求网址：https://syt.kwaixiaodian.com/rest/app/gateway/rank/list
请求方法：POST
状态代码：200 OK
远程地址：**************:443
引荐来源网址政策：strict-origin-when-cross-origin
content-encoding：br
content-type：application/json;charset=UTF-8
date：Sun, 27 Jul 2025 21:46:51 GMT
server：Tengine
x-kslogid：1753652811.202
x-request-id：753652811202007138
:authority：syt.kwaixiaodian.com
:method：POST
:path：/rest/app/gateway/rank/list
:scheme：https
accept：application/json, text/plain, */*
accept-encoding：gzip, deflate, br, zstd
accept-language：zh-CN,zh;q=0.9
content-length：566
content-type：application/json
cookie：[从cookies.txt读取]
kpf：PC_WEB
origin：https://syt.kwaixiaodian.com
priority：u=1, i
referer：https://syt.kwaixiaodian.com/mobile/seller/datacenter/rank?module=sytWebItemTopRank
sec-ch-ua："Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile：?0
sec-ch-ua-platform："Windows"
sec-fetch-dest：empty
sec-fetch-mode：cors
sec-fetch-site：same-origin
trace-id：1.0.0.1753652810635.5
user-agent：Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
阶段三：验证响应；
- 检查状态码是否为200，验证响应内容是否为有效的JSON格式，检查响应中是否包含预期的数据字段。​错误处理​：非200响应：记录错误信息并提示用户，请求超时：设置5秒超时，超时后重试(最多3次)，JSON解析失败：记录原始响应供调试。
阶段四：错误处理

| 错误类型     | 处理方式               | 日志级别     |
| -------- | ------------------ | -------- |
| 网络超时     | 自动重试(3次/间隔2秒)      | WARNING  |
| 403/401  | 通知更新cookies.txt    | ERROR    |
| 500系列    | 停止采集并报警            | CRITICAL |
| JSON解析失败 | 存储原始响应到error_log目录 | ERROR    |

阶段五：数据处理​
- 状态码是否为200的情况下解析响应数据并且展示在数据展示区域。

#### （四）总成交指数的采集方式
1.数据展示适配
- 数据展示适配:根据“总成交指数响应数据格式.md”适配数据展示区域的展示功能，字段映射规范：

| 响应字段          | 展示名称  | 数据类型  | 展示要求                             |
| ------------- | ----- | ----- | -------------------------------- |
| payOrderIndex | 总成交指数 | 整数    | 计算响应中payOrderIndex所有数值的总和，进行居中显示 |
| total         | 成交商家数 | 整数    | 居中显示                             |
| -             | 渠道占比  | 百分百显示 | 成交指数/总成交指数×100=渠道占比              |


2.采集流程
阶段一：请求准备
- 输入：从商品链接里提取商品ID,https://app.kwaixiaodian.com/merchant/shop/detail?id=24891558860698&hyId=kwaishop&layoutType=4,24891558860698是商品ID
- 处理：
    - 生成符合“总成交指数载荷源码格式.md"格式规范的JSON载荷
    - currentStartDay对应用户日期筛选器的开始时间，currentEndDay对应用户日期筛选器的结束时间。
- 输出：有效的请求载荷

阶段二：请求执行发送请求
请求网址：https://syt.kwaixiaodian.com/rest/app/gateway/rank/list
请求方法：POST
状态代码：200 OK
远程地址：**************:443
引荐来源网址政策：strict-origin-when-cross-origin
content-encodingbr
content-type：application/json;charset=UTF-8
date：Mon, 28 Jul 2025 15:32:54 GMT
server：Tengine
x-kslogid：1753716774.175
x-request-id：753716774175850618
:authority：syt.kwaixiaodian.com
:method：POST
:path：/rest/app/gateway/rank/list
:scheme：https
accept：application/json, text/plain, */*
accept-encoding：gzip, deflate, br, zstd
accept-language：zh-CN,zh;q=0.9
content-length：208
content-type：application/json
cookie：[从cookies.txt读取]
kpf：PC_WEB
origin：https://syt.kwaixiaodian.com
priority：u=1, i
referer：https://syt.kwaixiaodian.com/mobile/seller/datacenter/searchRankDetail?itemId=24891558860698&timeRange=CUSTOMIZED_WEEK&currentEndDay=2025-07-27&compareStartDay=2025-07-14&layoutType=4&currentStartDay=2025-07-21&compareEndDay=2025-07-20
sec-ch-ua："Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile：?0
sec-ch-ua-platform："Windows"
sec-fetch-dest：empty
sec-fetch-mode：cors
sec-fetch-site：same-origin
trace-id：1.0.0.1753716773575.1
user-agent：Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

阶段三：验证响应；
- 检查状态码是否为200，验证响应内容是否为有效的JSON格式，检查响应中是否包含预期的数据字段。​错误处理​：非200响应：记录错误信息并提示用户，请求超时：设置5秒超时，超时后重试(最多3次)，JSON解析失败：记录原始响应供调试。
阶段四：错误处理

| 错误类型     | 处理方式               | 日志级别     |
| -------- | ------------------ | -------- |
| 网络超时     | 自动重试(3次/间隔2秒)      | WARNING  |
| 403/401  | 通知更新cookies.txt    | ERROR    |
| 500系列    | 停止采集并报警            | CRITICAL |
| JSON解析失败 | 存储原始响应到error_log目录 | ERROR    |
阶段五：数据处理​
- 状态码是否为200的情况下解析响应数据并且展示在数据展示区域。
- 计算响应中payOrderIndex对应所有数值的总和，进行居中显示



# 三、采集逻辑优化方案  
#### 采集流程设计  
当用户点击"开始采集"按钮后，系统应按以下步骤执行数据采集：  
第一阶段：基础数据批量采集 
- 发送主要API请求，获取商品列表的基础数据  
- 基础数据包括：排名、标题、链接、支付件数、客单价、成交指数  
第二阶段：扩展数据逐条采集  
- 按商品顺序，逐个获取每个商品的扩展数据：  
1.总成交指数（通过总成交指数API）  
2.渠道占比（基于成交指数/总成交指数×100计算）  
3.成交商家数
- 每完成一个商品的扩展数据采集，立即更新该商品在表格中对应行的数据
#### 展示数据流程设计
- 按商品顺序，逐个获取每个商品的扩展数据，获取完成数据后再在数据展示区域显示
- 显示策略：基础数据获取完成后不可以立即显示在数据区域，需要等待每完成一个商品的扩展数据采集后，再显示在数据区域
- 每完成一个商品的所有扩展数据采集后，立即将该商品的完整数据（基础+扩展）显示在表格中  
- 状态提示显示"正在处理第X个商品..." 
- 显示时机: 基础数据获取完成后，不可立即显示在数据展示区域  
- 显示策略: 必须等待每个商品的扩展数据采集完成后，才将该商品的完整数据行显示在表格中  
- 确保每行显示的数据都是完整的（包含所有字段的实际值）  

# 四、主界面顶部添加标签页（Tab）
1.标签页布局：  
- 在主窗口顶部水平方向添加两个标签页
- 标签页1：命名为"商品采集"  
- 标签页2：命名为"查询商品成交量"  
2.内容迁移：  
- 将当前所有的UI界面元素（筛选条件、数据表格、按钮等）完整迁移到"商品采集"标签页中  
- 保持现有功能和布局不变，只是将其作为第一个标签页的内容 
3.新标签页内容 ：
- "查询商品成交量"标签页暂时显示占位内容  
- 可以显示文本"此功能正在开发中，敬请期待"或类似的提示信息  
- 为后续功能开发预留完整的界面空间  
4.技术要求：  
- 使用PyQt的QTabWidget组件实现标签页功能  
- 确保标签页切换流畅，不影响现有的数据采集功能  
- 保持现有的窗口大小和布局比例  
- 标签页样式应与整体界面风格保持一致  
5.功能保持：  
- 商品采集的所有现有功能必须完全保持不变  
- 数据采集、多线程处理、排名一致性等功能不受影响  
- 程序退出和资源清理机制保持不变

# 五、商品采集标签页增加设置项功能  
在商品采集标签页中添加两个数据过滤设置项，用于优化采集效率和数据展示： 
1.成交指数区间过滤设置
- 界面元素：添加两个输入框 "成交指数区间 【最小值】 ~ 【最大值】"
- 默认数值：最小值=1500，最大值=999999  
- 运行逻辑：  
* 由于商品排名按成交指数从高到低排序，当采集到某个商品的成交指数小于设置的最小值时，立即停止采集后续商品  
* 例如：设置500~100000，当遇到成交指数为小于500的商品时，停止采集该商品及其后面的所有商品


2.渠道占比区间过滤设置
- 界面元素：添加两个输入框 "渠道占比区间【最小值%】~【最大值%】"
- 默认数值：最小值=85%，最大值=105%  
- 运行逻辑：  
* 仍然采集所有商品的数据，但在数据展示区域只显示渠道占比在设定范围内的商品
* 例如：设置85%~100%，只有渠道占比在85%-100%之间的商品才会显示在数据表格中
* 不符合条件的商品数据被过滤掉，不在界面上展示

3.设置数据持久化  
- 将用户填写的四个数值（成交指数最小值、最大值，渠道占比最小值、最大值）保存到 `data\Category data.txt` 文件中  
- 软件启动时自动从 `data\Category data.txt` 文件加载这些设置值  
- 如果文件不存在或读取失败，使用默认数值  
- 确保用户的设置在重启软件后能够保持  

4.技术要求：  
- 在主界面添加设置区域，包含上述输入框  
- 实现数据验证，确保输入的是有效数值和百分比  
- 在数据采集过程中应用这些过滤条件  
- 实现设置的保存和加载功能

5.商品采集标签页中"数据过滤设置"区域的布局：  
5.1."数据过滤设置"区域放置到"条件筛选设置"区域的正下方,高度100px
5.2.布局：  
- 左侧50%区域：放置成交指数区间过滤设置和渠道占比区间过滤设置  
* 成交指数区间：【最小值】~ 【最大值】  
* 渠道占比区间：【最小值%】~ 【最大值%】  
- 右侧50%区域：放置所有功能按钮（登录、开始采集、停止采集、导出数据、解析类目）  
- 使用水平布局（QHBoxLayout）将左右两部分分开  
- 左侧过滤设置区域保持垂直布局，包含两行设置项  
- 右侧按钮区域保持原有的水平排列方式
- 确保两个区域的宽度比例为50%:50%  
- 保持原有的样式和功能不变  
6.预期效果：  
- 过滤设置更靠近筛选条件，逻辑更清晰  
- 按钮集中在右侧，操作更便捷  
- 整体布局更紧凑，空间利用更合理




# 七、商品成交量查询功能开发需求  

## 1. UI界面设计  
### 1.1 控制区域（占界面高度10%）  
- **下拉选择器**：时间范围选择  
- 选项：30天、60天、90天  
- 默认选择：30天  
- 功能按钮（从左到右排列）：  
- 导入链接：点击“导入链接”后自动粘贴剪切板的商品链接  
- 开始查询：启动数据采集流程 
- 停止查询：中断正在进行的查询
- 导出数据：将查询结果导出为Excel文件
  
### 1.2 数据展示区域（占界面高度90%）  
- **表格布局**：  
- 第1列：商品标题（固定宽度300px）  
- 第2列：商品链接（固定宽度150px）  
- 第3列及以后：日期列（格式MM-DD，动态生成，根据选择的天数确定列数）  
- 从当天日期-1开始，向前推算到选择的天数，
- 例如：选择30天，则显示30个日期列，从左到右排列  
- **表格特性**： 
- 支持水平和垂直滚动  
- 每行高度固定为40px
## 2. 数据展示规范  
根据"商品成交量响应数据格式.md"文件进行数据映射：  

| 响应字段        | 数据类型 | 展示规范                  | 处理逻辑         |
| ----------- | ---- | --------------------- | ------------ |
| itemTitle   | 字符串  | 最大显示50字符，超出部分用"..."省略 | 鼠标悬停显示完整标题   |
| payOrderCnt | 数值数组 | 居中显示                  | 计算数组中所有数值的总和 |


## 3. 数据采集流程  
### 3.1 阶段一：请求准备  
**输入参数**：  
- 商品链接列表（点击“导入链接”按钮后，链接显示在数据展示区域的“链接”列）  
- 查询日期范围（从下拉选择器获取）  
  
**处理逻辑**：  
- 从商品链接中提取itemId（正则表达式：`itemId=(\d+)`）  
- 根据"商品成交量数载荷源码格式.md"生成两个请求载荷：  
        - 成交量查询载荷  
        - 商品信息查询载荷  
**输出**：  
- 有效的JSON请求载荷  
### 3.2 阶段二：请求执行发送查询标题的请求
请求网址：https://syt.kwaixiaodian.com/rest/app/gateway/commodity/info
请求方法：POST
状态代码：200 OK
远程地址：**************:443
引荐来源网址政策：strict-origin-when-cross-origin
content-encoding：br
content-type：application/json;charset=UTF-8
date：Tue, 29 Jul 2025 15:17:37 GMT
server：Tengine
x-kslogid：1753802257.591
x-request-id：753802257591139128
:authority：syt.kwaixiaodian.com
:method：POST
:path：/rest/app/gateway/commodity/info
:scheme：https
accept：application/json, text/plain, */*
accept-encoding：gzip, deflate, br, zstd
accept-language：zh-CN,zh;q=0.9
content-length：116
content-type：application/json
cookie：[从cookies.txt读取]
kpf：PC_WEB
origin：https://syt.kwaixiaodian.com
priority：u=1, i
referer：https://syt.kwaixiaodian.com/mobile/seller/datacenter/searchRankDetail?itemId=24903052364858&timeRange=CUSTOMIZED_DAY&currentEndDay=2025-07-28&compareStartDay=2025-07-27&layoutType=4&currentStartDay=2025-07-28&compareEndDay=2025-07-27
sec-ch-ua："Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile：?0
sec-ch-ua-platform："Windows"
sec-fetch-dest：empty
sec-fetch-mode：cors
sec-fetch-site：same-origin
trace-id：1.0.0.1753802256734.2
user-agent：Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
### 3.3 阶段三：成交量数据查询  
阶段三：请求执行发送查询成交量的请求
请求网址：https://syt.kwaixiaodian.com/rest/app/gateway/rank/list
请求方法：POST
状态代码：200 OK
远程地址：**************:443
引荐来源网址政策：strict-origin-when-cross-origin
content-encoding：br
content-type：application/json;charset=UTF-8
date：Tue, 29 Jul 2025 15:17:37 GMT
server：Tengine
x-kslogid：1753802257.587
x-request-id：753802257587139128
:authority：syt.kwaixiaodian.com
:method：POST
:path：/rest/app/gateway/rank/list
:scheme：https
accept：application/json, text/plain, */*
accept-encoding：gzip, deflate, br, zstd
accept-language：zh-CN,zh;q=0.9
content-length：207
content-type：application/json
cookie：[从cookies.txt读取]
kpf：PC_WEB
origin：https://syt.kwaixiaodian.com
priority：u=1, i
referer：https://syt.kwaixiaodian.com/mobile/seller/datacenter/searchRankDetail?itemId=24903052364858&timeRange=CUSTOMIZED_DAY&currentEndDay=2025-07-28&compareStartDay=2025-07-27&layoutType=4&currentStartDay=2025-07-28&compareEndDay=2025-07-27
sec-ch-ua："Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
sec-ch-ua-mobile：?0
sec-ch-ua-platform："Windows"
sec-fetch-dest：empty
sec-fetch-mode：cors
sec-fetch-site：same-origin
trace-id：1.0.0.1753802256734.1
user-agent：Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36

### 3.4 阶段四：数据处理与展示  
**响应验证**：  
- 检查HTTP状态码是否为200  
- 验证响应JSON格式是否正确 

**数据处理**：  
- 解析itemTitle字段作为商品标题  
- 计算payOrderCnt数组的总和作为当日成交量  
- 按日期将成交量数据填入对应的表格列  
- 特殊响应情况处理

| 响应字段  | 响应结果 | 展示要求         |
| ----- | ---- | ------------ |
| total | 0    | 对应日期的结果显示“0” |

## 4. 执行流程
1.**批量处理**：按商品列表顺序逐个处理  
2.**实时更新**：每完成一个数据获取，完成一个响应的解析，立即在表格中显示结果。
3.**进度显示**：在界面上显示当前处理进度（如：正在处理第3/10个商品） 
4.**并发控制**：同时最多处理5个商品请求。
5.**处理顺序**：先查询第1-5序号链接的标题然后再开始查询每天的成交量，1-5查询结束后再查询6-10序号链接的标题，然后再开始查询每天的成交量，以此类推....





# 八、自动采集功能实现需求  
#### （八）全自动采集功能实现  
  
**1. UI界面增强**  
- 在商品采集标签页的按钮区域右侧添加复选框控件  
- 复选框标签文本："启用全自动采集功能"  
- 默认状态：未勾选  
- 状态持久化：每次程序启动时重置为未勾选状态（不保存到配置文件）  
- 位置：与现有功能按钮水平排列  
  
**2. 触发条件（必须同时满足）**  
- 用户勾选"启用全自动采集功能"复选框  
- 用户在类目筛选器中至少选择了一个具体类目（一级、二级、三级或四级类目中的任意一个，非空选项）  
- 点击"开始采集"按钮  
  
**3. 自动遍历执行逻辑**  
  
**3.1 确定起始点**  
- 识别用户选择的最深层级类目作为遍历起始点  
- 示例：用户选择"生鲜食品行业 > 零食坚果特产"，则从"零食坚果特产"开始  
  
**3.2 深度优先遍历规则**  
- **一级类目选择**：遍历该一级类目下的所有二级→三级→四级类目  
- 完成单个四级类目采集后，切换到同一三级类目下的下一个四级类目
- 完成单个三级类目下所有四级类目后，切换到同一二级类目下的下一个三级类目的四级类目
- 完成单个二级类目下所有三级类目后，切换到同一一级类目下的下一个二级类目的三级类目的四级类目
- 完成该一级类目下所有二级类目后，采集结束  
  
- **二级类目选择**：遍历该二级类目下的所有三级→四级类目  
- 完成单个四级类目采集后，切换到同一三级类目下的下一个四级类目
- 完成单个三级类目下所有四级类目后，切换到下一个三级类目的四级类目
- 完成该二级类目下所有三级类目后，采集结束  
  
- **三级类目选择**：遍历该三级类目下的所有四级类目  
- 完成单个四级类目采集后，切换到下一个四级类目  
- 完成该三级类目下所有四级类目后，采集结束  
  
- **四级类目选择**：仅采集该四级类目，采集完成后结束  
  
**3.3 遍历顺序**  
- 按类目在Category data.txt数据结构中的顺序依次处理  
- 每个类目采集完成后立即进行质量检查，然后继续下一个类目  
  
**4. 智能类目优化功能**  
  
**4.1 质量检查标准**  
- 获取该类目下排名第10名商品的成交指数  
- 如果第10名商品成交指数<100，标记为"低质量类目"  
- 如果该类目下商品总数<10，则检查最后一名商品的成交指数 
- 如果该类目下没有商品，则标记为"低质量类目"  
  
**4.2 自动删除机制**  
- 将低质量类目从内存中的类目数据结构中标记为待删除  
- 在日志文件中记录删除操作：类目名称、删除原因、删除时间  
- 将待删除的类目信息保存到临时文件中  
- 删除操作在程序重启后生效（避免影响当前采集流程）  
  
**5. 用户交互与状态管理**  
  
**5.1 状态显示**  
- 实时显示当前处理的类目路径："正在采集：生鲜食品行业 > 零食坚果特产 > 坚果炒货"  
- 显示整体进度："正在处理第X个类目，共Y个类目"  
- 自动采集过程中禁用类目筛选器的修改  
  
**5.2 控制功能**  
- 支持手动停止自动采集（点击"停止采集"按钮）  
- 停止后重新启用类目筛选器，用户可以手动选择类目继续普通采集  
- 采集完成后弹出对话框提示用户优化结果  
  
**6. 技术实现要点**  
- 保持现有的多线程采集机制  
- 自动采集时按顺序处理类目，避免并发冲突  
- 每完成一个类目采集后，立即检查数据质量并继续下一个类目  
- 错误处理：网络请求失败时记录日志，跳过当前类目，继续下一个