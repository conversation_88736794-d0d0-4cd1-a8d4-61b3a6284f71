# 自动采集功能说明

## 功能概述

全自动采集功能是快手采集工具的高级功能，可以根据用户选择的类目自动遍历所有子类目进行数据采集，并具备智能类目优化功能。

## 界面变化

在商品采集标签页的按钮区域右侧新增了"启用全自动采集功能"复选框。

## 使用方法

### 1. 启用条件

要使用自动采集功能，必须同时满足以下条件：

1. **勾选复选框**：勾选"启用全自动采集功能"复选框
2. **选择类目**：在类目筛选器中至少选择一个具体类目（一级、二级、三级或四级类目中的任意一个，不能为空）
3. **点击开始**：点击"开始采集"按钮

### 2. 操作步骤

1. 确保已解析类目数据（点击"解析类目"按钮）
2. 在类目筛选器中选择要采集的起始类目
3. 勾选"启用全自动采集功能"复选框
4. 设置数据过滤条件（成交指数区间、渠道占比区间）
5. 点击"开始采集"按钮

### 3. 自动遍历规则

系统会根据用户选择的类目层级自动确定遍历范围：

#### 一级类目选择
- 遍历该一级类目下的所有二级→三级→四级类目
- 按深度优先顺序：完成一个四级类目后，切换到同一三级类目下的下一个四级类目
- 完成一个三级类目后，切换到同一二级类目下的下一个三级类目
- 完成一个二级类目后，切换到下一个二级类目

#### 二级类目选择
- 遍历该二级类目下的所有三级→四级类目
- 按深度优先顺序处理

#### 三级类目选择
- 遍历该三级类目下的所有四级类目
- 按顺序依次处理每个四级类目

#### 四级类目选择
- 仅采集该四级类目，采集完成后结束

## 智能类目优化功能

### 质量检查标准

系统会对每个采集完成的类目进行质量检查：

1. **第10名商品检查**：如果该类目下排名第10名商品的成交指数 < 100，标记为低质量类目
2. **商品数量检查**：如果该类目下商品总数 < 10，则检查最后一名商品的成交指数
3. **无商品检查**：如果该类目下没有商品，直接标记为低质量类目

### 自动删除机制

- 低质量类目会被标记并记录到日志文件中
- 删除信息保存到临时文件 `data/low_quality_categories.json`
- 删除操作在程序重启后生效（避免影响当前采集流程）

## 状态显示

### 实时进度显示

- 显示当前处理的类目路径："正在采集：生鲜食品行业 > 零食坚果特产 > 坚果炒货"
- 显示整体进度："正在处理第X个类目，共Y个类目"

### 界面状态管理

- 自动采集过程中禁用类目筛选器的修改
- 自动采集过程中禁用复选框
- 支持手动停止（点击"停止采集"按钮）

## 控制功能

### 停止采集

- 点击"停止采集"按钮可以随时停止自动采集
- 停止后重新启用类目筛选器和复选框
- 用户可以手动选择类目继续普通采集

### 完成提示

- 采集完成后弹出对话框显示优化结果
- 显示处理的类目数量、低质量类目数量、采集数据条数

## 注意事项

1. **状态重置**：每次程序启动时，复选框会自动重置为未勾选状态
2. **数据保存**：自动采集过程中的所有数据会正常保存和显示
3. **错误处理**：网络请求失败时会记录日志，跳过当前类目，继续下一个
4. **性能考虑**：保持现有的多线程采集机制，但按顺序处理类目避免并发冲突

## 日志文件

- 类目优化日志：`logs/category_optimization_YYYYMMDD.log`
- 低质量类目信息：`data/low_quality_categories.json`

## 示例场景

假设用户选择了"生鲜食品行业"一级类目：

1. 系统会自动生成该一级类目下所有四级类目的列表
2. 按顺序采集每个四级类目的数据
3. 每完成一个类目采集，立即进行质量检查
4. 低质量类目会被标记但不影响后续采集
5. 所有类目采集完成后，显示优化结果

这样用户只需要选择一个起始类目，系统就能自动完成大量类目的数据采集工作。
