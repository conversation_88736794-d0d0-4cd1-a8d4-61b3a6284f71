# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
    ('类目响应数据.md', '.'),
    ('类目补充响应数据.md', '.'),
    ('响应数据格式.md', '.'),
    ('商品成交量响应数据格式.md', '.'),
    ('商品成交量数载荷源码格式.md', '.'),
    ('商品成交量查询功能说明.md', '.'),
    ('总成交指数响应数据格式.md', '.'),
    ('总成交指数载荷源码格式.md', '.'),
    ('分销销量 达人数响应数据格式.md', '.'),
    ('分销销量 达人数载荷源码格式.md', '.'),
    ('请求载荷源码格式.md', '.'),
    ('需求.md', '.'),
    ('README.md', '.'),
    ('data', 'data'),
    ('logs', 'logs')
    ],
    hiddenimports=[
    'PyQt5.QtCore',
    'PyQt5.QtGui',
    'PyQt5.QtWidgets',
    'PyQt5.QtWebEngineWidgets',
    'PyQt5.QtWebEngineCore',
    'requests',
    'pandas',
    'openpyxl',
    'json',
    'pathlib',
    'datetime',
    'concurrent.futures',
    'threading',
    'logging'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='快手采集工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    version='version_info.txt',
    icon=None,
)
