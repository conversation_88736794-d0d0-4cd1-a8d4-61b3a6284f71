@echo off
chcp 65001 >nul
title 快手采集工具 - Qt冲突修复与打包

echo.
echo ========================================
echo   快手采集工具 - Qt冲突修复与打包
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Python，请先安装Python 3.7+
    echo.
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✓ 检测到Python环境
python --version

echo.
echo 🔧 正在修复Qt版本冲突并打包...
echo.

:: 运行Qt冲突修复工具
python fix_qt_conflict.py

echo.
echo 修复和打包完成！
pause
