#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快手采集工具 - 整合后环境验证脚本
验证cookie_exporter.py整合到main.py后的环境是否正确
"""

import sys
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    version = sys.version_info
    print(f"   Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version >= (3, 7):
        print("   ✅ Python版本符合要求")
        return True
    else:
        print("   ❌ Python版本过低，需要3.7+")
        return False

def check_core_files():
    """检查核心文件"""
    print("\n📁 检查核心文件...")
    
    required_files = [
        "main.py",
        "data_collector.py",
        "product_query.py", 
        "category_parser.py"
    ]
    
    missing_files = []
    for file in required_files:
        if Path(file).exists():
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file} - 缺失")
            missing_files.append(file)
    
    # 检查cookie_exporter.py是否还存在（应该已整合）
    if Path("cookie_exporter.py").exists():
        print("   ⚠️ cookie_exporter.py - 仍然存在（已整合到main.py，可以删除）")
    else:
        print("   ✅ cookie_exporter.py - 已整合到main.py")
    
    return len(missing_files) == 0

def check_dependencies():
    """检查依赖包"""
    print("\n📦 检查依赖包...")
    
    dependencies = [
        ("PyQt5", "PyQt5"),
        ("PyQtWebEngine", "PyQt5.QtWebEngineWidgets"),
        ("requests", "requests"),
        ("pandas", "pandas"),
        ("openpyxl", "openpyxl"),
        ("pyinstaller", "PyInstaller")
    ]
    
    missing_deps = []
    
    for name, import_name in dependencies:
        try:
            __import__(import_name)
            print(f"   ✅ {name}")
        except ImportError:
            print(f"   ❌ {name} - 未安装")
            missing_deps.append(name)
    
    return len(missing_deps) == 0, missing_deps

def check_qt_conflict():
    """检查Qt版本冲突"""
    print("\n🔍 检查Qt版本冲突...")
    
    pyqt5_available = False
    pyqt6_available = False
    
    try:
        import PyQt5  # noqa: F401
        pyqt5_available = True
        print("   ✅ PyQt5 可用")
    except ImportError:
        print("   ❌ PyQt5 不可用")
    
    try:
        import PyQt6  # noqa: F401
        pyqt6_available = True
        print("   ⚠️ PyQt6 可用")
    except ImportError:
        print("   ✅ PyQt6 不可用（避免冲突）")
    
    if pyqt5_available and pyqt6_available:
        print("   ❌ 检测到Qt版本冲突！PyQt5和PyQt6同时存在")
        print("   💡 建议运行: python fix_qt_conflict.py")
        return False
    elif pyqt5_available:
        print("   ✅ 只有PyQt5，无冲突")
        return True
    elif pyqt6_available:
        print("   ⚠️ 只有PyQt6，建议切换到PyQt5以获得更好兼容性")
        return True
    else:
        print("   ❌ 没有可用的Qt版本")
        return False

def check_data_files():
    """检查数据文件"""
    print("\n📄 检查数据文件...")
    
    data_files = [
        "类目响应数据.md",
        "README.md"
    ]
    
    optional_files = [
        "类目补充响应数据.md",
        "响应数据格式.md",
        "商品成交量响应数据格式.md"
    ]
    
    # 检查必需文件
    missing_required = []
    for file in data_files:
        if Path(file).exists():
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file} - 缺失")
            missing_required.append(file)
    
    # 检查可选文件
    for file in optional_files:
        if Path(file).exists():
            print(f"   ✅ {file} (可选)")
        else:
            print(f"   ⚠️ {file} - 可选文件，缺失")
    
    # 检查目录
    directories = ["data", "logs"]
    for dir_name in directories:
        dir_path = Path(dir_name)
        if dir_path.exists():
            print(f"   ✅ {dir_name}/ 目录")
        else:
            print(f"   ⚠️ {dir_name}/ 目录不存在（运行时会自动创建）")
    
    return len(missing_required) == 0

def check_main_py_integration():
    """检查main.py中的整合情况"""
    print("\n🔗 检查main.py整合情况...")
    
    if not Path("main.py").exists():
        print("   ❌ main.py文件不存在")
        return False
    
    try:
        with open("main.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查是否包含WebEngine相关导入
        webengine_imports = [
            "QWebEngineView",
            "QWebEngineProfile", 
            "QWebEngineCookieStore"
        ]
        
        has_webengine = any(imp in content for imp in webengine_imports)
        
        if has_webengine:
            print("   ✅ 检测到WebEngine组件导入（Cookie功能已整合）")
        else:
            print("   ⚠️ 未检测到WebEngine组件导入")
        
        # 检查是否有Cookie相关类或方法
        cookie_indicators = [
            "CookieManager",
            "cookie_store",
            "export_cookies",
            "load_cookies"
        ]
        
        has_cookie_functionality = any(indicator in content for indicator in cookie_indicators)
        
        if has_cookie_functionality:
            print("   ✅ 检测到Cookie相关功能")
        else:
            print("   ⚠️ 未检测到明显的Cookie功能")
        
        return has_webengine or has_cookie_functionality
        
    except Exception as e:
        print(f"   ❌ 读取main.py失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 快手采集工具 - 整合后环境验证")
    print("=" * 50)
    
    all_checks_passed = True
    
    # 1. 检查Python版本
    if not check_python_version():
        all_checks_passed = False
    
    # 2. 检查核心文件
    if not check_core_files():
        all_checks_passed = False
    
    # 3. 检查依赖包
    deps_ok, missing_deps = check_dependencies()
    if not deps_ok:
        all_checks_passed = False
    
    # 4. 检查Qt冲突
    if not check_qt_conflict():
        all_checks_passed = False
    
    # 5. 检查数据文件
    if not check_data_files():
        all_checks_passed = False
    
    # 6. 检查main.py整合
    if not check_main_py_integration():
        all_checks_passed = False
    
    # 总结
    print("\n" + "=" * 50)
    if all_checks_passed:
        print("🎉 环境验证通过！可以开始打包")
        print("\n推荐打包命令:")
        print("   双击运行: 修复Qt冲突并打包.bat")
        print("   或运行: python fix_qt_conflict.py")
    else:
        print("❌ 环境验证失败，请解决以上问题")
        
        if not deps_ok:
            print(f"\n缺失依赖包: {', '.join(missing_deps)}")
            print("安装命令:")
            for dep in missing_deps:
                print(f"   pip install {dep}")
    
    print("\n" + "=" * 50)
    input("按回车键退出...")

if __name__ == "__main__":
    main()
