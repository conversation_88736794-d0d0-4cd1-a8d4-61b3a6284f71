#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快手数据采集模块
实现数据采集的核心功能，包括请求准备、执行、验证、错误处理和数据处理
"""

import json
import requests
import time
import logging
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

try:
    from PyQt5.QtCore import QThread, pyqtSignal
    PYQT_VERSION = 5
except ImportError:
    try:
        from PyQt6.QtCore import QThread, pyqtSignal
        PYQT_VERSION = 6
    except ImportError:
        # 如果没有PyQt，创建一个简单的基类
        class QThread:
            def __init__(self):
                pass
            def start(self):
                self.run()
            def run(self):
                pass
            def wait(self, timeout=None):
                pass
            def terminate(self):
                pass
            def isRunning(self):
                return False

        def pyqtSignal(*args):
            class Signal:
                def connect(self, func):
                    pass
                def emit(self, *args):
                    pass
            return Signal()

        print("警告：未安装PyQt，使用简化模式")
        PYQT_VERSION = 0


class DataCollector(QThread):
    """数据采集器"""
    
    # 信号定义
    progress_updated = pyqtSignal(str)  # 进度更新信号
    data_received = pyqtSignal(list)    # 数据接收信号（批量）
    single_item_ready = pyqtSignal(dict)  # 单个商品数据就绪信号（逐行显示）
    error_occurred = pyqtSignal(str)    # 错误发生信号
    collection_finished = pyqtSignal()  # 采集完成信号
    status_changed = pyqtSignal(str)    # 状态变化信号
    
    def __init__(self):
        super().__init__()
        self.filters = {}
        self.category_data = {}
        self.is_running = False
        self.should_stop = False

        # 过滤设置
        self.filter_settings = {
            "成交指数最小值": 1500,
            "成交指数最大值": 999999,
            "渠道占比最小值": 85,
            "渠道占比最大值": 105
        }

        # API配置
        self.api_url = "https://syt.kwaixiaodian.com/rest/app/gateway/rank/list"
        self.total_index_api_url = "https://syt.kwaixiaodian.com/rest/app/gateway/rank/list"  # 总成交指数API
        self.timeout = 5
        self.max_retries = 3
        self.retry_delay = 2

        # 设置日志
        self.setup_logging()

        # 多线程配置
        self.max_workers = 3  # 最大并发线程数
        self.thread_lock = threading.Lock()  # 线程锁
        self.executor = None  # 线程池执行器
        
    def setup_logging(self):
        """设置日志"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / f"collector_{datetime.now().strftime('%Y%m%d')}.log", encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def set_filters(self, filters: Dict[str, str]):
        """设置筛选条件"""
        self.filters = filters.copy()
        self.logger.info(f"设置筛选条件: {self.filters}")

    def set_filter_settings(self, filter_settings: Dict[str, float]):
        """设置过滤设置"""
        self.filter_settings = filter_settings.copy()
        self.logger.info(f"设置过滤设置: {self.filter_settings}")
        
    def load_category_data(self):
        """加载类目数据"""
        try:
            category_file = Path("data/Category data.txt")
            if category_file.exists():
                with open(category_file, 'r', encoding='utf-8') as f:
                    self.category_data = json.load(f)
                self.logger.info("类目数据加载成功")
                return True
            else:
                self.logger.warning("类目数据文件不存在")
                return False
        except Exception as e:
            self.logger.error(f"加载类目数据失败: {e}")
            return False
            
    def load_cookies(self) -> Dict[str, str]:
        """加载Cookie"""
        # 如果正在停止，直接返回空字典
        if self.should_stop:
            return {}

        cookies = {}
        cookie_files = ["data/cookies.txt", "data/cookies2.txt"]
        
        for cookie_file in cookie_files:
            try:
                if Path(cookie_file).exists():
                    with open(cookie_file, 'r', encoding='utf-8') as f:
                        content = f.read().strip()
                        if content:
                            # 解析Cookie字符串
                            for item in content.split(';'):
                                if '=' in item:
                                    key, value = item.strip().split('=', 1)
                                    cookies[key] = value
            except Exception as e:
                self.logger.error(f"读取Cookie文件 {cookie_file} 失败: {e}")
                
        if cookies:
            self.logger.info(f"成功加载 {len(cookies)} 个Cookie")
        else:
            self.logger.warning("未找到有效的Cookie")
            
        return cookies
        
    def generate_request_payload(self) -> Optional[Dict[str, Any]]:
        """生成请求载荷"""
        try:
            # 阶段一：请求准备
            self.progress_updated.emit("正在准备请求载荷...")
            
            # 验证筛选条件
            if not self.validate_filters():
                return None
                
            # 解析日期
            date_range = self.parse_date_range()
            if not date_range:
                return None
                
            # 构建基础载荷
            payload = {
                "module": "sytWebItemTopRank",
                "timeRange": "CUSTOMIZED_WEEK",
                "currentStartDay": date_range["current_start"],
                "currentEndDay": date_range["current_end"],
                "compareStartDay": date_range["compare_start"],
                "compareEndDay": date_range["compare_end"],
                "param": [],
                "pageNum": 1,
                "pageSize": 100
            }
            
            # 添加筛选参数
            self.add_filter_params(payload)
            
            self.logger.info(f"生成请求载荷: {json.dumps(payload, ensure_ascii=False)}")
            return payload
            
        except Exception as e:
            self.logger.error(f"生成请求载荷失败: {e}")
            self.error_occurred.emit(f"生成请求载荷失败: {e}")
            return None
            
    def validate_filters(self) -> bool:
        """验证筛选条件有效性"""
        required_filters = ["日期", "售卖渠道", "售卖形式", "品牌商品", "大牌大补"]
        
        for filter_name in required_filters:
            if filter_name not in self.filters or not self.filters[filter_name]:
                self.error_occurred.emit(f"缺少必要的筛选条件: {filter_name}")
                return False
                
        return True
        
    def parse_date_range(self) -> Optional[Dict[str, str]]:
        """解析日期范围"""
        try:
            date_str = self.filters.get("日期", "")

            # 处理"本周"和"上周"格式
            if date_str.startswith("本周") or date_str.startswith("上周"):
                # 提取括号内的日期
                start_idx = date_str.find("(")
                end_idx = date_str.find(")")
                if start_idx != -1 and end_idx != -1:
                    date_str = date_str[start_idx+1:end_idx]
            
            # 解析日期范围，支持多种格式
            if " - " in date_str:
                # 格式: "YYYY-MM-DD - YYYY-MM-DD"
                parts = date_str.split(" - ")
                if len(parts) == 2:
                    current_start = parts[0].strip()
                    current_end = parts[1].strip()

                    # 计算对比周期（前一周）
                    start_date = datetime.strptime(current_start, "%Y-%m-%d")
                    compare_start_date = start_date - timedelta(days=7)
                    compare_end_date = compare_start_date + timedelta(days=6)

                    return {
                        "current_start": current_start,
                        "current_end": current_end,
                        "compare_start": compare_start_date.strftime("%Y-%m-%d"),
                        "compare_end": compare_end_date.strftime("%Y-%m-%d")
                    }
            elif "-" in date_str:
                # 格式: "YYYY-MM-DD-YYYY-MM-DD"
                parts = date_str.split("-")
                if len(parts) >= 6:
                    current_start = f"{parts[0]}-{parts[1]}-{parts[2]}"
                    current_end = f"{parts[3]}-{parts[4]}-{parts[5]}"

                    # 计算对比周期（前一周）
                    start_date = datetime.strptime(current_start, "%Y-%m-%d")
                    compare_start_date = start_date - timedelta(days=7)
                    compare_end_date = compare_start_date + timedelta(days=6)

                    return {
                        "current_start": current_start,
                        "current_end": current_end,
                        "compare_start": compare_start_date.strftime("%Y-%m-%d"),
                        "compare_end": compare_end_date.strftime("%Y-%m-%d")
                    }
                    
            self.error_occurred.emit("日期格式无效")
            return None
            
        except Exception as e:
            self.logger.error(f"解析日期范围失败: {e}")
            self.error_occurred.emit(f"解析日期失败: {e}")
            return None
            
    def add_filter_params(self, payload: Dict[str, Any]):
        """添加筛选参数到载荷"""
        params = payload["param"]
        
        # 添加行业ID（一级类目）
        first_category = self.filters.get("一级类目", "")
        if first_category:
            params.append({"code": "industryId", "value": [first_category]})

            # 只有当选择了二级类目时，才添加categoryLevel1Id
            second_category = self.filters.get("二级类目", "")
            if second_category:
                # 使用二级类目的ID作为categoryLevel1Id
                second_category_id = self.find_category_id_by_name(second_category)
                if second_category_id:
                    params.append({"code": "categoryLevel1Id", "value": [second_category_id]})
            # 如果只选择了一级类目，不添加categoryLevel1Id参数
                
        # 添加其他类目级别
        self.add_category_levels(params)
        
        # 添加售卖相关参数
        self.add_sale_params(params)
        
    def get_category_id(self, category_name: str) -> Optional[str]:
        """获取类目ID"""
        try:
            if category_name in self.category_data:
                key = self.category_data[category_name].get("key", "")
                self.logger.info(f"找到类目 '{category_name}' 的ID: {key}")
                return key
            else:
                self.logger.warning(f"未找到类目 '{category_name}'")
                return None
        except Exception as e:
            self.logger.error(f"获取类目ID失败: {e}")
            return None

    def get_first_child_category_id(self, category_name: str) -> Optional[str]:
        """获取第一个子类目的ID"""
        try:
            if category_name in self.category_data:
                children = self.category_data[category_name].get("children", {})
                if children:
                    # 获取第一个子类目
                    first_child = next(iter(children.values()))
                    child_id = first_child.get("key", "")
                    self.logger.info(f"找到类目 '{category_name}' 的第一个子类目ID: {child_id}")
                    return child_id
            return None
        except Exception as e:
            self.logger.error(f"获取第一个子类目ID失败: {e}")
            return None

    def add_category_levels(self, params: List[Dict[str, Any]]):
        """添加类目级别参数"""
        # 只处理三级和四级类目，二级类目已在主逻辑中处理
        category_levels = [
            ("三级类目", "categoryLevel2Id"),  # 三级类目对应categoryLevel2Id
            ("四级类目", "categoryLevel3Id")   # 四级类目对应categoryLevel3Id
        ]

        for filter_name, param_code in category_levels:
            category_name = self.filters.get(filter_name, "")
            if category_name:
                category_id = self.find_category_id_by_name(category_name)
                if category_id:
                    params.append({"code": param_code, "value": [category_id]})
                    
    def find_category_id_by_name(self, name: str) -> Optional[str]:
        """根据名称查找类目ID"""
        try:
            # 在类目树中递归查找
            def search_in_tree(tree_data, target_name):
                if isinstance(tree_data, dict):
                    # 检查当前节点
                    if tree_data.get('name') == target_name:
                        return tree_data.get('key')

                    # 递归搜索子节点
                    if 'children' in tree_data:
                        children = tree_data['children']
                        if isinstance(children, dict):
                            for child in children.values():
                                result = search_in_tree(child, target_name)
                                if result:
                                    return result
                        elif isinstance(children, list):
                            for child in children:
                                result = search_in_tree(child, target_name)
                                if result:
                                    return result
                return None

            # 在整个类目树中搜索
            for category in self.category_data.values():
                result = search_in_tree(category, name)
                if result:
                    return result

            return None

        except Exception as e:
            self.logger.error(f"查找类目ID失败: {e}")
            return None
        
    def add_sale_params(self, params: List[Dict[str, Any]]):
        """添加售卖相关参数"""
        # 售卖渠道映射
        channel_map = {
            "全部": "all",
            "直播间": "直播间", 
            "短视频": "短视频",
            "商品卡": "商品卡"
        }
        
        # 售卖形式映射（自卖=否，分销=是）
        type_map = {
            "全部": "all",
            "自卖": "否",
            "分销": "是"
        }
        
        # 添加售卖渠道
        channel = self.filters.get("售卖渠道", "全部")
        params.append({"code": "saleChannel", "value": [channel_map.get(channel, "all")]})
        
        # 添加售卖形式
        sale_type = self.filters.get("售卖形式", "全部")
        params.append({"code": "saleType", "value": [type_map.get(sale_type, "all")]})
        
        # 添加品牌商品
        brand_item = self.filters.get("品牌商品", "全部")
        params.append({"code": "brandItem", "value": [type_map.get(brand_item, "all")]})
        
        # 添加大牌大补
        subsidy_item = self.filters.get("大牌大补", "全部")
        params.append({"code": "subsidyItem", "value": [type_map.get(subsidy_item, "all")]})

    def run(self):
        """主运行方法 - 优化的两阶段采集流程"""
        try:
            self.is_running = True
            self.should_stop = False

            self.progress_updated.emit("开始数据采集...")
            self.status_changed.emit("采集中")

            # 加载必要数据
            if not self.load_category_data():
                self.error_occurred.emit("加载类目数据失败")
                return

            # 第一阶段：基础数据批量采集
            self.progress_updated.emit("第一阶段：获取基础数据...")
            basic_data = self.collect_basic_data()
            if not basic_data:
                self.error_occurred.emit("基础数据采集失败")
                return

            # 第二阶段：扩展数据逐条采集并逐行显示
            self.progress_updated.emit("第二阶段：获取扩展数据...")
            self.collect_extended_data_and_display(basic_data)

        except Exception as e:
            self.logger.error(f"采集过程出错: {e}")
            self.error_occurred.emit(f"采集失败: {e}")
        finally:
            self.is_running = False
            # 只在采集真正结束时发送状态变化信号，避免与进度更新冲突
            self.collection_finished.emit()

    def collect_basic_data(self) -> Optional[List[Dict[str, Any]]]:
        """第一阶段：基础数据批量采集"""
        try:
            # 生成请求载荷
            payload = self.generate_request_payload()
            if not payload:
                return None

            # 执行请求
            response_data = self.execute_request(payload)
            if not response_data:
                return None

            # 提取基础数据列表
            data_list = response_data.get('data', {}).get('data', [])
            if not data_list:
                self.logger.warning("响应中没有数据")
                self.error_occurred.emit("未获取到数据")
                return None

            self.logger.info(f"成功获取 {len(data_list)} 条基础数据")
            return data_list

        except Exception as e:
            self.logger.error(f"基础数据采集失败: {e}")
            self.error_occurred.emit(f"基础数据采集失败: {e}")
            return None

    def collect_extended_data_and_display(self, basic_data: List[Dict[str, Any]]):
        """第二阶段：扩展数据多线程采集并逐行显示"""
        try:
            total_items = len(basic_data)
            completed_count = 0

            # 应用成交指数过滤
            filtered_data = self.apply_trade_index_filter(basic_data)

            if not filtered_data:
                self.progress_updated.emit("所有商品都被成交指数过滤条件过滤掉了")
                return

            # 使用可控制的线程池进行并发处理
            self.executor = ThreadPoolExecutor(max_workers=self.max_workers)
            try:
                # 提交所有任务（使用过滤后的数据）
                future_to_item = {}
                for index, item in enumerate(filtered_data):
                    if self.should_stop:
                        break

                    # 处理基础数据
                    processed_item = self.process_basic_item_data(item)
                    if processed_item:
                        # 提交扩展数据获取任务
                        future = self.executor.submit(self.process_single_item_with_extended_data, processed_item, item, index + 1)
                        future_to_item[future] = (processed_item, index + 1)

                # 处理完成的任务
                for future in as_completed(future_to_item):
                    if self.should_stop:
                        # 取消未完成的任务
                        for f in future_to_item:
                            if not f.done():
                                f.cancel()
                        break

                    try:
                        processed_item, item_index = future_to_item[future]
                        result = future.result(timeout=1)  # 设置超时

                        if result:
                            # 更新完成计数
                            with self.thread_lock:
                                completed_count += 1
                                self.progress_updated.emit(f"已完成 {completed_count}/{total_items} 个商品")

                            # 立即显示完整的商品数据
                            self.single_item_ready.emit(processed_item)

                    except Exception as e:
                        self.logger.error(f"处理商品数据时出错: {e}")

            finally:
                # 确保线程池被正确关闭
                if self.executor:
                    self.executor.shutdown(wait=False)  # 不等待任务完成
                    self.executor = None

            self.progress_updated.emit(f"采集完成，共处理 {completed_count} 个商品")

        except Exception as e:
            self.logger.error(f"扩展数据采集失败: {e}")
            self.error_occurred.emit(f"扩展数据采集失败: {e}")

    def apply_trade_index_filter(self, basic_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """应用成交指数过滤"""
        try:
            filtered_data = []
            min_trade_index = self.filter_settings["成交指数最小值"]

            for item in basic_data:
                # 获取成交指数
                pay_order_index = item.get("payOrderIndex", {})
                if isinstance(pay_order_index, dict):
                    source_value = pay_order_index.get("sourceValue", 0)

                    # 检查是否满足最小成交指数条件
                    if source_value >= min_trade_index:
                        filtered_data.append(item)
                    else:
                        # 由于数据按成交指数从高到低排序，遇到小于最小值的商品时停止采集
                        self.logger.info(f"遇到成交指数 {source_value} 小于最小值 {min_trade_index}，停止采集")
                        break
                else:
                    # 如果没有成交指数数据，跳过该商品
                    continue

            self.logger.info(f"成交指数过滤：原始 {len(basic_data)} 条，过滤后 {len(filtered_data)} 条")
            return filtered_data

        except Exception as e:
            self.logger.error(f"应用成交指数过滤失败: {e}")
            return basic_data  # 过滤失败时返回原始数据

    def process_single_item_with_extended_data(self, processed_item: Dict[str, Any], original_item: Dict[str, Any], item_index: int) -> bool:
        """在线程中处理单个商品的扩展数据"""
        try:
            # 获取扩展数据
            self.collect_and_fill_extended_data(processed_item, original_item)
            return True

        except Exception as e:
            self.logger.error(f"处理第{item_index}个商品的扩展数据失败: {e}")
            return False

    def process_basic_item_data(self, item: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """处理单个商品的基础数据"""
        try:
            # 根据响应数据格式.md进行基础字段映射
            processed_item = {
                "排名": item.get("rankIndex", ""),
                "标题": self.truncate_text(item.get("itemTitle", ""), 50),
                "链接": item.get("itemLinkUrl", ""),
                "支付件数": self.format_number(item.get("payItemNum", 0)),
                "成交指数": self.format_source_value(item.get("payOrderIndex", {})),
                "总成交指数": "",  # 扩展数据，稍后填充
                "渠道占比": "",    # 扩展数据，稍后填充
                "商家ID": str(item.get("sellerId", "")),  # 新增商家ID字段
                "成交商家数": "",  # 扩展数据，稍后填充
                "类目": self.get_display_category()
            }

            return processed_item

        except Exception as e:
            self.logger.error(f"处理基础数据项失败: {e}")
            return None

    def collect_and_fill_extended_data(self, processed_item: Dict[str, Any], original_item: Dict[str, Any]):
        """获取并填充扩展数据（线程安全）"""
        try:
            # 获取商品链接和ID
            item_url = original_item.get("itemLinkUrl", "")
            if not item_url:
                # 如果没有链接，扩展数据保持为空
                return

            # 提取商品ID
            item_id = self.extract_item_id_from_url(item_url)
            if not item_id:
                # 如果无法提取商品ID，扩展数据保持为空
                return

            # 获取原始成交指数用于计算渠道占比
            original_source_value = original_item.get("payOrderIndex", {}).get("sourceValue", 0)

            # 获取总成交指数数据（线程安全）
            total_index_data = self.fetch_total_index_data_thread_safe(item_id)
            if total_index_data:
                # 处理总成交指数数据
                total_index_result = self.process_total_index_data(total_index_data, original_source_value)

                # 填充扩展数据（使用线程锁确保安全）
                with self.thread_lock:
                    processed_item["总成交指数"] = total_index_result["总成交指数"]
                    processed_item["渠道占比"] = total_index_result["渠道占比"]
                    processed_item["成交商家数"] = total_index_result["成交商家数"]

        except Exception as e:
            self.logger.error(f"获取扩展数据失败: {e}")
            # 扩展数据获取失败时，保持为空字符串

    def execute_request(self, payload: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """执行网络请求"""
        try:
            # 阶段二：请求执行
            self.progress_updated.emit("正在发送请求...")

            # 加载Cookie
            cookies = self.load_cookies()
            if not cookies:
                self.error_occurred.emit("未找到有效的Cookie，请先登录")
                return None

            # 构建请求头
            headers = self.build_request_headers()

            # 执行请求（带重试机制）
            for attempt in range(self.max_retries):
                if self.should_stop:
                    return None

                try:
                    self.progress_updated.emit(f"发送请求 (尝试 {attempt + 1}/{self.max_retries})...")

                    response = requests.post(
                        self.api_url,
                        json=payload,
                        headers=headers,
                        cookies=cookies,
                        timeout=self.timeout
                    )

                    # 阶段三：验证响应
                    if self.validate_response(response):
                        return response.json()
                    else:
                        if attempt < self.max_retries - 1:
                            self.progress_updated.emit(f"请求失败，{self.retry_delay}秒后重试...")
                            time.sleep(self.retry_delay)
                        continue

                except requests.exceptions.Timeout:
                    self.logger.warning(f"请求超时 (尝试 {attempt + 1})")
                    if attempt < self.max_retries - 1:
                        self.progress_updated.emit(f"请求超时，{self.retry_delay}秒后重试...")
                        time.sleep(self.retry_delay)
                    else:
                        self.error_occurred.emit("请求超时，请检查网络连接")

                except requests.exceptions.RequestException as e:
                    self.logger.error(f"网络请求异常: {e}")
                    if attempt < self.max_retries - 1:
                        time.sleep(self.retry_delay)
                    else:
                        self.error_occurred.emit(f"网络请求失败: {e}")

            return None

        except Exception as e:
            self.logger.error(f"执行请求失败: {e}")
            self.error_occurred.emit(f"请求执行失败: {e}")
            return None

    def build_request_headers(self) -> Dict[str, str]:
        """构建请求头"""
        return {
            "Accept": "application/json, text/plain, */*",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Accept-Language": "zh-CN,zh;q=0.9",
            "Content-Type": "application/json",
            "KPF": "PC_WEB",
            "Origin": "https://syt.kwaixiaodian.com",
            "Referer": "https://syt.kwaixiaodian.com/mobile/seller/datacenter/rank?module=sytWebItemTopRank",
            "Sec-CH-UA": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            "Sec-CH-UA-Mobile": "?0",
            "Sec-CH-UA-Platform": '"Windows"',
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "Trace-ID": f"1.0.0.{int(time.time() * 1000)}.5",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        }

    def build_total_index_headers(self, item_id: str) -> Dict[str, str]:
        """构建总成交指数请求头"""
        # 获取日期筛选条件用于构建Referer
        date_range = self.parse_date_range()

        if date_range:
            start_day = date_range["current_start"]
            end_day = date_range["current_end"]
        else:
            start_day = "2025-07-21"
            end_day = "2025-07-27"

        # 根据需求文档中的referer格式构建
        referer = f"https://syt.kwaixiaodian.com/mobile/seller/datacenter/searchRankDetail?itemId={item_id}&timeRange=CUSTOMIZED_WEEK&currentEndDay={end_day}&compareStartDay=2025-07-14&layoutType=4&currentStartDay={start_day}&compareEndDay=2025-07-20"

        return {
            "Accept": "application/json, text/plain, */*",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Accept-Language": "zh-CN,zh;q=0.9",
            "Content-Type": "application/json",
            "KPF": "PC_WEB",
            "Origin": "https://syt.kwaixiaodian.com",
            "Referer": referer,
            "Sec-CH-UA": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            "Sec-CH-UA-Mobile": "?0",
            "Sec-CH-UA-Platform": '"Windows"',
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "Trace-ID": f"1.0.0.{int(time.time() * 1000)}.1",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        }

    def validate_response(self, response: requests.Response) -> bool:
        """验证响应"""
        try:
            # 检查状态码
            if response.status_code != 200:
                self.handle_http_error(response.status_code)
                return False

            # 检查响应内容类型
            content_type = response.headers.get('content-type', '')
            if 'application/json' not in content_type:
                self.logger.error(f"响应内容类型错误: {content_type}")
                self.error_occurred.emit("服务器响应格式错误")
                return False

            # 验证JSON格式
            try:
                data = response.json()
            except json.JSONDecodeError as e:
                self.logger.error(f"JSON解析失败: {e}")
                self.save_error_response(response.text)
                self.error_occurred.emit("响应数据格式错误")
                return False

            # 检查响应数据结构
            if not isinstance(data, dict) or 'result' not in data:
                self.logger.error("响应数据结构错误")
                self.error_occurred.emit("响应数据结构错误")
                return False

            # 检查业务结果
            if data.get('result') != 1:
                error_msg = data.get('error_msg', '未知错误')
                self.logger.error(f"业务请求失败: {error_msg}")
                self.error_occurred.emit(f"请求失败: {error_msg}")
                return False

            return True

        except Exception as e:
            self.logger.error(f"响应验证失败: {e}")
            self.error_occurred.emit(f"响应验证失败: {e}")
            return False

    def handle_http_error(self, status_code: int):
        """处理HTTP错误"""
        # 阶段四：错误处理
        if status_code in [401, 403]:
            self.logger.error(f"认证失败 (状态码: {status_code})")
            self.error_occurred.emit("登录已过期，请重新登录")
        elif status_code >= 500:
            self.logger.critical(f"服务器错误 (状态码: {status_code})")
            self.error_occurred.emit("服务器内部错误，请稍后重试")
        else:
            self.logger.error(f"HTTP错误 (状态码: {status_code})")
            self.error_occurred.emit(f"请求失败 (错误码: {status_code})")

    def save_error_response(self, response_text: str):
        """保存错误响应到日志目录"""
        try:
            error_dir = Path("error_log")
            error_dir.mkdir(exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            error_file = error_dir / f"error_response_{timestamp}.txt"

            with open(error_file, 'w', encoding='utf-8') as f:
                f.write(response_text)

            self.logger.info(f"错误响应已保存到: {error_file}")

        except Exception as e:
            self.logger.error(f"保存错误响应失败: {e}")



    def truncate_text(self, text: str, max_length: int) -> str:
        """截断文本到指定长度"""
        if len(text) <= max_length:
            return text
        return text[:max_length-3] + "..."

    def format_number(self, number: int) -> str:
        """格式化数字（千位分隔符）"""
        try:
            return f"{number:,}"
        except:
            return str(number)



    def format_source_value(self, source_value_obj: Dict[str, Any]) -> str:
        """格式化成交指数（取整数）"""
        try:
            source_value = source_value_obj.get("sourceValue", 0)
            return str(int(source_value))
        except:
            return "0"

    def extract_item_id_from_url(self, url: str) -> Optional[str]:
        """从商品链接中提取商品ID"""
        try:
            import re

            # 快手商品链接格式: https://app.kwaixiaodian.com/merchant/shop/detail?id=24891558860698&hyId=kwaishop&layoutType=4
            # 提取id参数的值
            pattern = r'[?&]id=(\d+)'
            match = re.search(pattern, url)

            if match:
                return match.group(1)

            # 如果没有匹配到，尝试其他可能的格式
            # 例如: /detail/24891558860698 这样的路径格式
            pattern2 = r'/detail/(\d+)'
            match2 = re.search(pattern2, url)

            if match2:
                return match2.group(1)

            return None

        except Exception as e:
            self.logger.error(f"提取商品ID失败: {e}")
            return None

    def generate_total_index_payload(self, item_id: str) -> Optional[Dict[str, Any]]:
        """生成总成交指数请求载荷"""
        try:
            # 获取日期筛选条件
            date_range = self.parse_date_range()

            if not date_range:
                self.logger.error("无效的日期范围")
                return None

            start_day = date_range["current_start"]
            end_day = date_range["current_end"]

            # 根据总成交指数载荷源码格式.md生成载荷
            payload = {
                "module": "sytWebItemTopRank4Seller",
                "pageNum": 1,
                "pageSize": 10,
                "timeRange": "CUSTOMIZED_WEEK",
                "currentStartDay": start_day,
                "currentEndDay": end_day,
                "param": [
                    {
                        "code": "itemId",
                        "value": [item_id]
                    }
                ]
            }

            return payload

        except Exception as e:
            self.logger.error(f"生成总成交指数载荷失败: {e}")
            return None

    def fetch_total_index_data(self, item_id: str) -> Optional[Dict[str, Any]]:
        """获取总成交指数数据"""
        try:
            # 生成请求载荷
            payload = self.generate_total_index_payload(item_id)
            if not payload:
                return None

            # 获取请求头和Cookie
            headers = self.build_total_index_headers(item_id)
            cookies = self.load_cookies()
            if not cookies:
                self.logger.error("未找到有效的Cookie")
                return None

            # 发送请求
            for attempt in range(self.max_retries):
                try:
                    self.progress_updated.emit(f"正在获取商品 {item_id} 的总成交指数...")

                    response = requests.post(
                        self.total_index_api_url,
                        json=payload,
                        headers=headers,
                        cookies=cookies,
                        timeout=self.timeout
                    )

                    if response.status_code == 200:
                        data = response.json()
                        if data.get("result") == 1:
                            return data
                        else:
                            self.logger.warning(f"总成交指数API返回错误: {data.get('error_msg', '未知错误')}")
                            return None
                    else:
                        self.logger.warning(f"总成交指数请求失败，状态码: {response.status_code}")

                except requests.exceptions.Timeout:
                    self.logger.warning(f"总成交指数请求超时，第 {attempt + 1} 次重试")
                    if attempt < self.max_retries - 1:
                        time.sleep(self.retry_delay)
                        continue
                except Exception as e:
                    self.logger.error(f"总成交指数请求异常: {e}")
                    break

            return None

        except Exception as e:
            self.logger.error(f"获取总成交指数数据失败: {e}")
            return None

    def fetch_total_index_data_thread_safe(self, item_id: str) -> Optional[Dict[str, Any]]:
        """线程安全的获取总成交指数数据"""
        try:
            # 检查是否应该停止
            if self.should_stop:
                return None

            # 生成请求载荷
            payload = self.generate_total_index_payload(item_id)
            if not payload:
                return None

            # 获取请求头和Cookie（线程安全）
            headers = self.build_total_index_headers(item_id)
            cookies = self.load_cookies()
            if not cookies or self.should_stop:
                return None

            # 发送请求（减少重试次数以提高并发效率）
            max_retries = 2  # 多线程环境下减少重试次数
            for attempt in range(max_retries):
                # 检查是否应该停止
                if self.should_stop:
                    return None

                try:
                    response = requests.post(
                        self.total_index_api_url,
                        json=payload,
                        headers=headers,
                        cookies=cookies,
                        timeout=self.timeout
                    )

                    if response.status_code == 200:
                        data = response.json()
                        if data.get("result") == 1:
                            return data
                        else:
                            self.logger.warning(f"总成交指数API返回错误: {data.get('error_msg', '未知错误')}")
                            return None
                    else:
                        self.logger.warning(f"总成交指数请求失败，状态码: {response.status_code}")

                except requests.exceptions.Timeout:
                    if attempt < max_retries - 1:
                        time.sleep(0.5)  # 减少重试延迟
                        continue
                except Exception as e:
                    self.logger.error(f"总成交指数请求异常: {e}")
                    break

            return None

        except Exception as e:
            self.logger.error(f"获取总成交指数数据失败: {e}")
            return None

    def process_total_index_data(self, total_index_response: Dict[str, Any], original_source_value: int) -> Dict[str, str]:
        """处理总成交指数数据"""
        try:
            result = {
                "总成交指数": "0",
                "成交商家数": "0",
                "渠道占比": "0.00%"
            }

            if not total_index_response or "data" not in total_index_response:
                return result

            data = total_index_response["data"]

            # 获取成交商家数 (total字段)
            total_sellers = data.get("total", 0)
            result["成交商家数"] = str(total_sellers)

            # 计算总成交指数 (所有payOrderIndex的sourceValue总和)
            total_pay_order_index = 0
            if "data" in data and isinstance(data["data"], list):
                for item in data["data"]:
                    pay_order_index = item.get("payOrderIndex", {})
                    if isinstance(pay_order_index, dict):
                        source_value = pay_order_index.get("sourceValue", 0)
                        total_pay_order_index += source_value

            result["总成交指数"] = str(total_pay_order_index)

            # 计算渠道占比 (成交指数/总成交指数×100)
            if total_pay_order_index > 0:
                channel_ratio = (original_source_value / total_pay_order_index) * 100
                result["渠道占比"] = f"{channel_ratio:.2f}%"

            return result

        except Exception as e:
            self.logger.error(f"处理总成交指数数据失败: {e}")
            return {
                "总成交指数": "0",
                "成交商家数": "0",
                "渠道占比": "0.00%"
            }

    def get_display_category(self) -> str:
        """获取显示的类目信息"""
        try:
            categories = []

            # 按级别收集类目
            for level in ["一级类目", "二级类目", "三级类目", "四级类目"]:
                category = self.filters.get(level, "")
                if category:
                    categories.append(category)

            return " > ".join(categories) if categories else ""

        except Exception as e:
            self.logger.error(f"获取显示类目失败: {e}")
            return ""

    def start_collection(self):
        """开始采集"""
        if not self.is_running:
            self.start()

    def stop_collection(self):
        """停止采集"""
        try:
            self.should_stop = True
            self.progress_updated.emit("正在停止采集...")

            if self.is_running:
                # 给线程池一些时间来完成当前任务
                self.wait(3000)  # 等待3秒
                if self.isRunning():
                    self.logger.info("强制终止采集线程")
                    self.terminate()  # 强制终止
                    self.wait(1000)  # 再等待1秒确保终止

            self.is_running = False
            self.progress_updated.emit("采集已停止")

        except Exception as e:
            self.logger.error(f"停止采集时出错: {e}")
            self.is_running = False

    def cleanup(self):
        """清理资源"""
        try:
            # 设置停止标志
            self.should_stop = True

            # 强制关闭线程池
            if self.executor:
                self.logger.info("正在关闭线程池...")
                self.executor.shutdown(wait=False)  # 立即关闭，不等待任务完成
                self.executor = None

            # 停止采集
            if self.is_running:
                self.stop_collection()

            # 确保线程完全停止
            if self.isRunning():
                self.terminate()
                self.wait(1000)  # 等待1秒
                if self.isRunning():
                    # 如果还在运行，强制终止
                    self.terminate()

            self.logger.info("DataCollector资源清理完成")

        except Exception as e:
            self.logger.error(f"清理资源时出错: {e}")
